-- <PERSON><PERSON>ntüleme Test Dosyası

if CLIENT then
    -- Test fonksiyonu - parti menüsünü açar
    concommand.Add("test_party_menu", function()
        PartyMenu()
    end)
    
    -- Test fonksiyonu - rank oluşturma menüsünü açar
    concommand.Add("test_rank_create", function()
        if OpenRankCreateMenu then
            OpenRankCreateMenu()
        else
            print("OpenRankCreateMenu fonksiyonu bulunamadı!")
        end
    end)
    
    -- Test fonksiyonu - üye yönetimi menüsünü açar
    concommand.Add("test_member_management", function()
        if OpenMemberManagementMenu then
            OpenMemberManagementMenu()
        else
            print("OpenMemberManagementMenu fonksiyonu bulunamadı!")
        end
    end)
    
    -- Debug fonksiyonu - oyuncunun parti durumunu gösterir
    concommand.Add("debug_player_party", function()
        local ply = LocalPlayer()
        print("=== Oyuncu Parti Durumu ===")
        print("Steam ID: " .. ply:SteamID64())
        print("Parti ID: " .. tostring(ply:GetParty()))
        print("Parti Adı: " .. tostring(ply:GetPartyName()))
        print("Parti Rütbesi: " .. tostring(ply:GetPartyRank()))
        print("Parti Rütbe Adı: " .. tostring(ply:GetPartyRankName()))
        
        if ply:GetParty() then
            print("\nİzinler:")
            local permissions = {
                "kick", "invite", "promote", "demote", 
                "createRank", "transferLeadership", "disbandParty"
            }
            
            for _, perm in ipairs(permissions) do
                local hasPerm = ply:HasPartyPermission(perm)
                print("  " .. perm .. ": " .. tostring(hasPerm))
            end
        end
    end)
    
    print("Menü test komutları yüklendi:")
    print("  - test_party_menu: Parti menüsünü aç")
    print("  - test_rank_create: Rank oluşturma menüsünü aç")
    print("  - test_member_management: Üye yönetimi menüsünü aç")
    print("  - debug_player_party: Oyuncu parti durumunu göster")
end

if SERVER then
    -- Sunucu tarafı test komutları
    concommand.Add("give_party_permissions", function(ply, cmd, args)
        if not ply:IsAdmin() then return end
        
        local targetName = args[1]
        if not targetName then
            print("Kullanım: give_party_permissions <oyuncu_adı>")
            return
        end
        
        local targetPlayer = nil
        for _, p in pairs(player.GetAll()) do
            if string.lower(p:Nick()) == string.lower(targetName) then
                targetPlayer = p
                break
            end
        end
        
        if not targetPlayer then
            print("Oyuncu bulunamadı: " .. targetName)
            return
        end
        
        -- Oyuncuyu parti lideri yap
        if not targetPlayer:GetParty() then
            targetPlayer:Startparty("Test Partisi")
        end
        
        print("Oyuncuya parti lideri yetkisi verildi: " .. targetPlayer:Nick())
    end)
    
    concommand.Add("create_test_ranks", function(ply, cmd, args)
        if not ply:IsAdmin() then return end
        
        if not ply:GetParty() then
            ply:Startparty("Test Partisi")
        end
        
        -- Test rütbeleri oluştur
        ply:CreatePartyRank("moderator", "Moderatör", Color(100, 150, 255), {
            kick = true,
            invite = true,
            promote = false,
            demote = false,
            createRank = false,
            transferLeadership = false,
            disbandParty = false
        })
        
        ply:CreatePartyRank("vip", "VIP Üye", Color(255, 215, 0), {
            kick = false,
            invite = true,
            promote = false,
            demote = false,
            createRank = false,
            transferLeadership = false,
            disbandParty = false
        })
        
        print("Test rütbeleri oluşturuldu!")
    end)
    
    print("Sunucu test komutları yüklendi:")
    print("  - give_party_permissions <oyuncu_adı>: Oyuncuya parti lideri yetkisi ver")
    print("  - create_test_ranks: Test rütbeleri oluştur")
end
