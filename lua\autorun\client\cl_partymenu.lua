--GUI base created by billy
--https://scriptfodder.com/users/view/76561198040894045/scripts

AddCSLuaFile()

parties = {}
	CreateClientConVar("color_phalo_r", 0, true)
	CreateClientConVar("color_phalo_g", 255, true)
	CreateClientConVar("color_phalo_b", 0, true)
	CreateClientConVar("color_phalo_a", 255, true)
	if party.defaultlowend == true then 
		CreateClientConVar("party_lowend", 1, true)
	else 
		CreateClientConVar("party_lowend", 0, true)
	end
	CreateClientConVar("party_showhud", 0, true)
	
	
	local hcol = Color(	GetConVar( "color_phalo_r" ):GetInt(hr), GetConVar( "color_phalo_g" ):GetInt(hg), GetConVar( "color_phalo_b" ):GetInt(hb), GetConVar( "color_phalo_a" ):GetInt(ha)) or Color(0,255,0,255)	
	local hr, hg, hb, ha = tonumber(hcol.r) or 0, tonumber(hcol.g) or 0, tonumber(hcol.b) or 0, tonumber(hcol.a) or 255
	
	function HaloColorUpdate()
	local hr, hg, hb, ha = tostring(tonumber(hcol.r) or 0), tostring(tonumber(hcol.g) or 0), tostring(tonumber(hcol.b) or 0), tostring(tonumber(hcol.a) or 255)
		GetConVar( "color_phalo_r" ):SetInt(hr)  
		GetConVar( "color_phalo_g" ):SetInt(hg)
		GetConVar( "color_phalo_b" ):SetInt(hb)
		GetConVar( "color_phalo_a" ):SetInt(ha)
	end

local PartyFrame

		hook.Add( "Think", "Partykeylistener", function()
			if party.partymenubutton then
				if  input.IsKeyDown( party.partymenubutton ) and (PartyFrame == nil || !PartyFrame:IsVisible())  then
					PartyMenu()
				end
			end
		end )

			
			net.Receive("party", function(len, CLIENT)
				parties = net.ReadTable()
				--print("allparties")
				--PrintTable(parties)
			end)
			
			net.Receive("oneparty", function(len, CLIENT)
				local partystring = net.ReadString()
				parties[partystring] = net.ReadTable()
				if parties[partystring].name == "DeleteMe" then
					parties[partystring] = nil
				end
				--print("oneparty")
				--PrintTable(parties)
			end)
			
			net.Receive("onepartytoparty", function(len, CLIENT)
				local partystring = net.ReadString()
				parties[partystring] = net.ReadTable()
				--print("onepartytoparty")
				--PrintTable(parties)
			end)
				
			net.Receive( "partiesmenu", function( len, pl )
				PartyMenu()
			end)
			
			
			net.Receive( "joinrequest", function( len, pl )
				local requestedid = net.ReadString()  
				requestMenu(requestedid)
			end)

			net.Receive( "partyinvite", function( len, pl )
				local invitedby = net.ReadString()  
				partyinvite(invitedby)
			end)
		
		
function partyinvite(invitedby)
	local Frame = vgui.Create( "DFrame" )
	Frame:SetPos( -500, ScrH()/4 ) 
	Frame:SetSize( 200, 400 ) 
	Frame:SetTitle( party.language["Invited to join a party"] ) 
	Frame:SetVisible( true )
	Frame:SetDraggable( true ) 
	Frame:ShowCloseButton( false )
	--Frame:MakePopup() 
	Frame:SetDeleteOnClose(true)
	Frame:SetVerticalScrollbarEnabled(false)
	Frame:LerpPositions(20,false)
	Frame:SetPos( 0, ScrH()/4 ) 

	function Frame:Paint( w, h )
		draw.RoundedBox( 5, 0, 0, w, h, party.backgroundcolor )
		draw.RoundedBox(5,2,2,w-4,h-4,Color(0,0,0,100))
		draw.RoundedBox(5,4,4,w-8,h-8,Color(0,0,0,100))
		draw.RoundedBox(5,6,6,w-12,h-12,Color(0,0,0,100))
		draw.RoundedBox(5,8,8,w-16,h-16,Color(0,0,0,100))
		Frame:InvalidateLayout(true)
		Frame:SizeToChildren( true, true )
	end
	local DButtonclose = vgui.Create( "DButton" )
	DButtonclose:SetParent( Frame )
	DButtonclose:SetPos( Frame:GetSize(), 5) 
	DButtonclose:SetText( "X" )
	DButtonclose:SetSize( 20, 20 )
	DButtonclose:SetTextColor(Color( 255,255,255) )
	--DButtonclose:Dock(RIGHT)
	DButtonclose.DoClick = function()
		--LocalPlayer():ConCommand("answerinvite "..invitedby .. " false")
		net.Start( "AnswerInvite" )
		net.WriteString( invitedby)
		net.WriteBool( false)
		net.SendToServer()
	
		Frame:Close()
	end
	function DButtonclose:Paint(w,h)
		if !DButtonclose:IsHovered() then
			draw.RoundedBox(0,0,0,w,h, party.buttoncolor)
			draw.RoundedBox(0,1,1,w-2,h-2,Color(0,0,0,100))
			draw.RoundedBox(0,2,2,w-4,h-4,Color(0,0,0,100))
			draw.RoundedBox(0,3,3,w-6,h-6,Color(0,0,0,100))
			draw.RoundedBox(0,4,4,w-8,h-8,Color(0,0,0,100))
		end
		if DButtonclose:IsHovered() then
			draw.RoundedBox(0,0,0,w,h, party.buttonhovercolor)
			draw.RoundedBox(0,1,1,w-2,h-2,Color(0,0,0,100))
			draw.RoundedBox(0,2,2,w-4,h-4,Color(0,0,0,100))
			draw.RoundedBox(0,3,3,w-6,h-6,Color(0,0,0,100))
			draw.RoundedBox(0,4,4,w-8,h-8,Color(0,0,0,100))
		end
	end
	
	local panel2 = vgui.Create("DPanel",Frame)
		panel2:Dock(TOP)
		panel2:SetSize(Frame:GetSize(), 50 )
		panel2:SetPos()
		
		function panel2:Paint(w,h)
			panel2:InvalidateLayout(true)
		panel2:SizeToChildren( true, true )
		end
		
	local DLabel2 = vgui.Create( "DLabel", panel2 )
	DLabel2:Dock(TOP)
		if player.GetBySteamID64(invitedby) != false then
			DLabel2:SetText( player.GetBySteamID64(invitedby):Nick())
		else 
			DLabel2:SetText("A Team")
		end
	local DLabel3 = vgui.Create( "DLabel", panel2 )
	DLabel3:Dock(TOP)
	DLabel3:SetText( party.language["Has invited you to their party."])
	 
	local DLabel3 = vgui.Create( "DLabel", panel2 )
	DLabel3:Dock(TOP)
	DLabel3:SetText( party.language["Accept?"])
	
	local panel3 = vgui.Create("DPanel",panel2)
		panel3:Dock(TOP)
		panel3:SetSize(panel2:GetSize(), 20 )
		panel3:SetPos()
		
	function panel3:Paint(w,h)
		panel3:InvalidateLayout(true)
		panel3:SizeToChildren( true, true )
	end
	
	
	 local DButtonyes = vgui.Create( "DButton" )
	DButtonyes:SetParent( panel3 )
	DButtonyes:Dock(LEFT)
	DButtonyes:SetText( party.language["YES"] )
	DButtonyes:SetSize( panel3:GetSize()/2, 20 )
	DButtonyes:SetTextColor(Color( 255,255,255) )
	DButtonyes.DoClick = function()
		--LocalPlayer():ConCommand("answerinvite "..invitedby .. " true")
		net.Start( "AnswerInvite" )
		net.WriteString( invitedby)
		net.WriteBool(true)
		net.SendToServer()
		Frame:Close()
	end
	function DButtonyes:Paint(w,h)
			if !DButtonyes:IsHovered() then
			draw.RoundedBox(0,0,0,w,h, party.buttoncolor)
			draw.RoundedBox(0,1,1,w-2,h-2,Color(0,0,0,100))
			draw.RoundedBox(0,2,2,w-4,h-4,Color(0,0,0,100))
			draw.RoundedBox(0,3,3,w-6,h-6,Color(0,0,0,100))
			draw.RoundedBox(0,4,4,w-8,h-8,Color(0,0,0,100))
		end
		if DButtonyes:IsHovered() then
			draw.RoundedBox(0,0,0,w,h, party.buttonhovercolor)
			draw.RoundedBox(0,1,1,w-2,h-2,Color(0,0,0,100))
			draw.RoundedBox(0,2,2,w-4,h-4,Color(0,0,0,100))
			draw.RoundedBox(0,3,3,w-6,h-6,Color(0,0,0,100))
			draw.RoundedBox(0,4,4,w-8,h-8,Color(0,0,0,100))
		end
	end
	
	
	local DButtonno = vgui.Create( "DButton" )
	DButtonno:SetParent( panel3 )
	DButtonno:Dock(RIGHT)
	DButtonno:SetText( party.language["NO"] )
	DButtonno:SetSize( panel3:GetSize()/2, 20 )
	DButtonno:SetTextColor(Color( 255,255,255) )
	DButtonno.DoClick = function()
		Frame:Close()
		--LocalPlayer():ConCommand("answerinvite "..invitedby .. " false")
		net.Start( "AnswerInvite" )
		net.WriteString( invitedby)
		net.WriteBool( false)
		net.SendToServer()
	end
		function DButtonno:Paint(w,h)
			if !DButtonno:IsHovered() then
			draw.RoundedBox(0,0,0,w,h, party.buttoncolor)
			draw.RoundedBox(0,1,1,w-2,h-2,Color(0,0,0,100))
			draw.RoundedBox(0,2,2,w-4,h-4,Color(0,0,0,100))
			draw.RoundedBox(0,3,3,w-6,h-6,Color(0,0,0,100))
			draw.RoundedBox(0,4,4,w-8,h-8,Color(0,0,0,100))
		end
		if DButtonno:IsHovered() then
			draw.RoundedBox(0,0,0,w,h, party.buttonhovercolor)
			draw.RoundedBox(0,1,1,w-2,h-2,Color(0,0,0,100))
			draw.RoundedBox(0,2,2,w-4,h-4,Color(0,0,0,100))
			draw.RoundedBox(0,3,3,w-6,h-6,Color(0,0,0,100))
			draw.RoundedBox(0,4,4,w-8,h-8,Color(0,0,0,100))
		end
	end	
end
		
		
		
		
		
		
		

function requestMenu(requestedid)
	local Frame = vgui.Create( "DFrame" )
	Frame:SetPos( -500, ScrH()/4 ) 
	Frame:SetSize( 150, 400 ) 
	Frame:SetTitle(  party.language["Request To Join Your Party"] ) 
	Frame:SetVisible( true )
	Frame:SetDraggable( true ) 
	Frame:ShowCloseButton( false )
	--Frame:MakePopup() 
	Frame:SetDeleteOnClose(true)
	Frame:SetVerticalScrollbarEnabled(false)
	Frame:LerpPositions(20,false)
	Frame:SetPos( 0, ScrH()/4 ) 

	function Frame:Paint( w, h )
		draw.RoundedBox( 5, 0, 0, w, h, party.backgroundcolor )
		draw.RoundedBox(5,2,2,w-4,h-4,Color(0,0,0,100))
		draw.RoundedBox(5,4,4,w-8,h-8,Color(0,0,0,100))
		draw.RoundedBox(5,6,6,w-12,h-12,Color(0,0,0,100))
		draw.RoundedBox(5,8,8,w-16,h-16,Color(0,0,0,100))
		Frame:InvalidateLayout(true)
		Frame:SizeToChildren( true, true )
	end
	local DButtonclose = vgui.Create( "DButton" )
	DButtonclose:SetParent( Frame )
	DButtonclose:SetPos( Frame:GetSize(), 5) 
	DButtonclose:SetText( "X" )
	DButtonclose:SetSize( 20, 20 )
	DButtonclose:SetTextColor(Color( 255,255,255) )
	--DButtonclose:Dock(RIGHT)
	DButtonclose.DoClick = function()
	 Frame:Close()
	end
	function DButtonclose:Paint(w,h)
		if !DButtonclose:IsHovered() then
			draw.RoundedBox(0,0,0,w,h, party.buttoncolor)
			draw.RoundedBox(0,1,1,w-2,h-2,Color(0,0,0,100))
			draw.RoundedBox(0,2,2,w-4,h-4,Color(0,0,0,100))
			draw.RoundedBox(0,3,3,w-6,h-6,Color(0,0,0,100))
			draw.RoundedBox(0,4,4,w-8,h-8,Color(0,0,0,100))
		end
		if DButtonclose:IsHovered() then
			draw.RoundedBox(0,0,0,w,h, party.buttonhovercolor)
			draw.RoundedBox(0,1,1,w-2,h-2,Color(0,0,0,100))
			draw.RoundedBox(0,2,2,w-4,h-4,Color(0,0,0,100))
			draw.RoundedBox(0,3,3,w-6,h-6,Color(0,0,0,100))
			draw.RoundedBox(0,4,4,w-8,h-8,Color(0,0,0,100))
		end
	end
	
	local panel2 = vgui.Create("DPanel",Frame)
		panel2:Dock(TOP)
		panel2:SetSize(Frame:GetSize(), 50 )
		panel2:SetPos()
		
		function panel2:Paint(w,h)
			panel2:InvalidateLayout(true)
		panel2:SizeToChildren( true, true )
		end
		
	local DLabel2 = vgui.Create( "DLabel", panel2 )
	DLabel2:Dock(TOP)
	DLabel2:SetText( player.GetBySteamID64(requestedid):Nick())
			
	local DLabel3 = vgui.Create( "DLabel", panel2 )
	DLabel3:Dock(TOP)
	DLabel3:SetText( party.language["Would like to join your party"])
	 
	local DLabel3 = vgui.Create( "DLabel", panel2 )
	DLabel3:Dock(TOP)
	DLabel3:SetText( party.language["Accept?"])
	
	local panel3 = vgui.Create("DPanel",panel2)
		panel3:Dock(TOP)
		panel3:SetSize(panel2:GetSize(), 20 )
		panel3:SetPos()
		
	function panel3:Paint(w,h)
		panel3:InvalidateLayout(true)
		panel3:SizeToChildren( true, true )
	end
	
	
	 local DButtonyes = vgui.Create( "DButton" )
	DButtonyes:SetParent( panel3 )
	DButtonyes:Dock(LEFT)
	DButtonyes:SetText( party.language["YES"] )
	DButtonyes:SetSize( panel3:GetSize()/2, 20 )
	DButtonyes:SetTextColor(Color( 255,255,255) )
	DButtonyes.DoClick = function()
	--LocalPlayer():ConCommand("answerjoinrequest "..requestedid .. " true")
		net.Start( "RequestedJoin" )
		net.WriteString( requestedid)
		net.WriteBool( true)
		net.SendToServer()
	 Frame:Close()
	end
	function DButtonyes:Paint(w,h)
			if !DButtonyes:IsHovered() then
			draw.RoundedBox(0,0,0,w,h, party.buttoncolor)
			draw.RoundedBox(0,1,1,w-2,h-2,Color(0,0,0,100))
			draw.RoundedBox(0,2,2,w-4,h-4,Color(0,0,0,100))
			draw.RoundedBox(0,3,3,w-6,h-6,Color(0,0,0,100))
			draw.RoundedBox(0,4,4,w-8,h-8,Color(0,0,0,100))
		end
		if DButtonyes:IsHovered() then
			draw.RoundedBox(0,0,0,w,h, party.buttonhovercolor)
			draw.RoundedBox(0,1,1,w-2,h-2,Color(0,0,0,100))
			draw.RoundedBox(0,2,2,w-4,h-4,Color(0,0,0,100))
			draw.RoundedBox(0,3,3,w-6,h-6,Color(0,0,0,100))
			draw.RoundedBox(0,4,4,w-8,h-8,Color(0,0,0,100))
		end
	end
	 
	local DButtonno = vgui.Create( "DButton" )
	DButtonno:SetParent( panel3 )
	DButtonno:Dock(RIGHT)
	DButtonno:SetText( party.language["NO"] )
	DButtonno:SetSize( panel3:GetSize()/2, 20 )
	DButtonno:SetTextColor(Color( 255,255,255) )
	DButtonno.DoClick = function()
	 Frame:Close()
	--LocalPlayer():ConCommand("answerjoinrequest "..requestedid .. " false")
		net.Start( "RequestedJoin" )
		net.WriteString( requestedid)
		net.WriteBool( false)
		net.SendToServer()
	 
	end
		function DButtonno:Paint(w,h)
			if !DButtonno:IsHovered() then
			draw.RoundedBox(0,0,0,w,h, party.buttoncolor)
			draw.RoundedBox(0,1,1,w-2,h-2,Color(0,0,0,100))
			draw.RoundedBox(0,2,2,w-4,h-4,Color(0,0,0,100))
			draw.RoundedBox(0,3,3,w-6,h-6,Color(0,0,0,100))
			draw.RoundedBox(0,4,4,w-8,h-8,Color(0,0,0,100))
		end
		if DButtonno:IsHovered() then
			draw.RoundedBox(0,0,0,w,h, party.buttonhovercolor)
			draw.RoundedBox(0,1,1,w-2,h-2,Color(0,0,0,100))
			draw.RoundedBox(0,2,2,w-4,h-4,Color(0,0,0,100))
			draw.RoundedBox(0,3,3,w-6,h-6,Color(0,0,0,100))
			draw.RoundedBox(0,4,4,w-8,h-8,Color(0,0,0,100))
		end
	end	 
end


	 party.framesizex = 500
	 party.framesizey = 300
	 party.rightsize = 250
	 
	 
function PartyMenu()
	local Partytab
	local Partymembs
	PartyFrame = vgui.Create("PartyFrame")
	PartyFrame:SetPos( -500 , ScrH()/4 ) 
	PartyFrame:SetSize( party.framesizex, party.framesizey ) 
	PartyFrame:Configured()
	PartyFrame:SetTitle( party.language["Party Menu"] ) 
	PartyFrame:SetVisible( true )
	PartyFrame:SetDraggable( true )
	PartyFrame:ShowCloseButton(true)
	PartyFrame:MakePopup() 
	PartyFrame:SetDeleteOnClose(true)
	PartyFrame:SetVerticalScrollbarEnabled(false)
	PartyFrame:LerpPositions(20,false)
	PartyFrame:Center()
	
	Partytab = vgui.Create("DPanel", PartyFrame)
	Partytab:SetSize( 250 )
	Partytab:Dock(RIGHT)
	local DLabel = vgui.Create( "PartyLabel", Partytab )
		DLabel:SetColor(Color( 0, 40 , 0 ))
		DLabel:SetText( party.language["Welcome to the party menu!"] )
		DLabel:SizeToContents()
		DLabel:Center()
		DLabel:AlignTop()
	local DLabel = vgui.Create( "PartyLabel", Partytab )
		DLabel:SetColor(Color( 0, 40 , 0 ))
		DLabel:SetText( party.language["An easy way for you to"] )
		DLabel:SizeToContents()
		DLabel:Center()
		DLabel:AlignTop(30)
	local DLabel = vgui.Create( "PartyLabel", Partytab )
		DLabel:SetColor(Color( 0, 40 , 0 ))
		DLabel:SetText( party.language["team up with your friends!"] )
		DLabel:SizeToContents()
		DLabel:Center()
		DLabel:AlignTop(45)	
		


	
	
local Partylist = vgui.Create("PartyCategories", PartyFrame)
	local x, y = PartyFrame:GetSize()
	Partylist:SetSize( 250 )
	Partylist:SetPos( 0, 24 )
	Partylist:Dock(LEFT)
	Partylist:DockMargin(-1,0,0,0)
	Partylist:NewItem(party.language["Start Party"] , Color(75,200,75),function()
		PartyFrame:SetSize( party.framesizex, party.framesizey ) 
		PartyFrame:ShowCloseButton(false)
		PartyFrame:Configured()
		PartyFrame:ShowCloseButton(true)
		if Partytab then
			Partytab:Remove()
		end
		Partytab = vgui.Create("DPanel", PartyFrame)
		Partytab:SetSize( party.rightsize )
		Partytab:Dock(LEFT)
		local DLabel = vgui.Create( "PartyLabel", Partytab )
			DLabel:SetColor(Color( 0, 40 , 0 ))
			DLabel:SetText( party.language["WARNING!"] )
			DLabel:SizeToContents()
			DLabel:Center()
			DLabel:AlignTop(30)
		local DLabel = vgui.Create( "PartyLabel", Partytab )
			DLabel:SetColor(Color( 0, 40 , 0 ))
			DLabel:SetText( party.language["By starting a new party"] )
			DLabel:SizeToContents()
			DLabel:Center()
			DLabel:AlignTop(45)	
		local DLabel = vgui.Create( "PartyLabel", Partytab )
			DLabel:SetColor(Color( 0, 40 , 0 ))
			DLabel:SetText( party.language["you will be removed from"] )
			DLabel:SizeToContents()
			DLabel:Center()
			DLabel:AlignTop(60)			
		local DLabel = vgui.Create( "PartyLabel", Partytab )
			DLabel:SetColor(Color( 0, 40 , 0 ))
			DLabel:SetText( party.language["your current party."] )
			DLabel:SizeToContents()
			DLabel:Center()
			DLabel:AlignTop(75)	
			
		local Dtextname
		
		local DButtonStart = vgui.Create( "PartyButton")
		DButtonStart:SetParent( Partytab )
		DButtonStart:SetText( party.language["Start A New Party"] ) 
		DButtonStart:SetSize( Partytab:GetSize()+2, 50 )
		DButtonStart:SetPos( 0, 0 ) 
		DButtonStart:SetTextColor(Color( 255,255,255) )
		DButtonStart:Dock(BOTTOM)
		DButtonStart:DockMargin(-1,0,0,-1)
		DButtonStart.DoClick = function()
			--LocalPlayer():ConCommand("StartParty \"" .. string.Left( Dtextname:GetValue(), 20))
				net.Start( "StartParty" )
				net.WriteString(string.Left( Dtextname:GetValue(), 20))
				net.SendToServer()
			PartyFrame:Close()
		end
		
		Dtextname = vgui.Create( "PartyTextBox")
		Dtextname:SetParent( Partytab )
		Dtextname:SetSize( Partytab:GetSize()+2, 25 )
		Dtextname:SetPos( 0, 0 ) 
		Dtextname:SetTextColor(Color( 0,0,0) )
		Dtextname:Dock(BOTTOM)
		Dtextname:DockMargin(-1,0,0,-1)
	
			local DLabel = vgui.Create( "PartyLabel", Partytab )
			DLabel:SetColor(Color( 0, 40 , 0 ))
			DLabel:SetText( party.language["Party Name"] )
			DLabel:SizeToContents()
			DLabel:Dock(BOTTOM)
			DLabel:Center()
			
	end, true)
	
	Partylist:NewItem(party.language["Join Party"] , Color(75,75,200),function()
		if Partytab then
			Partytab:Remove()
		end
		PartyFrame:SetSize( party.framesizex, party.framesizey ) 
		PartyFrame:ShowCloseButton(false)
		PartyFrame:Configured()
		PartyFrame:ShowCloseButton(true)
		Partytab = vgui.Create("PartyCategories", PartyFrame)
		Partytab:SetSize( party.rightsize )
		Partytab:Dock(LEFT)

		for v,k in pairs (parties) do
			if !table.HasValue(parties[v].members, LocalPlayer():SteamID64()) then
				if !parties[v].Autogrouped then 
					Partytab:NewItem(parties[v].name , Color(75,200,75), function()
						PartyFrame:SetSize(party.framesizex + 150 , party.framesizey )
						PartyFrame:ShowCloseButton(false)
						PartyFrame:Configured()
						PartyFrame:ShowCloseButton(true)
						if Partymembs then
							Partymembs:Remove()
						end
						Partymembs = vgui.Create("DPanel", PartyFrame) 
						Partymembs:SetSize(150, party.framesizey - 20)
						Partymembs:Dock(RIGHT)
						
						local DButtonjoin = vgui.Create( "PartyButton", Partymembs )
						DButtonjoin:SetPos( party.framesizex - 20, 0 ) 
						DButtonjoin:SetText( party.language["Members"]  )
						DButtonjoin:SetSize( 20, 20 )
						DButtonjoin:SetTextColor(Color( 255,255,255) )
						DButtonjoin:Dock(TOP)
						DButtonjoin:SetTextColor(Color(0,0,0))
						DButtonjoin.DoClick = function()
						end
					
						DButtonjoin.Paint = function()
							surface.DrawRect(0,0,DButtonjoin:GetWide(),DButtonjoin:GetTall())
							surface.SetDrawColor(Color(26,26,26))
							surface.DrawOutlinedRect(0,0,DButtonjoin:GetWide(),DButtonjoin:GetTall())
						end
					
						
						local Partymembslist = vgui.Create("PartyCategories", Partymembs) 
							Partymembslist:SetSize(150, party.framesizey - 45)
							Partymembslist:Dock(TOP)
							if parties[v] then
								if parties[v].members then
									for v,k in pairs(parties[v].members) do
										if player.GetBySteamID64(k) != false then
											Partymembslist:NewItem(player.GetBySteamID64(k):Nick(), Color(255,255,255,0), function()
											end, false)
										end
									end	
								end
							end
						local DButtonjoin = vgui.Create( "PartyButton", Partymembs )
						DButtonjoin:SetPos( party.framesizex - 20, 0 ) 
						DButtonjoin:SetText( party.language["Request Join"] )
						if parties[v] then
							if table.HasValue(parties[v].members, LocalPlayer():SteamID64()) then
								DButtonjoin:SetDisabled(true)
							end
						end
						if party.AutoGroupedJobs[v] then
							DButtonjoin:SetDisabled(true)
						end
						DButtonjoin:SetSize( 20, 20 )
						DButtonjoin:SetTextColor(Color( 255,255,255) )
						DButtonjoin:Dock(BOTTOM)
						DButtonjoin:DockMargin(-1,0,-1,-1)
						DButtonjoin.DoClick = function()
							--LocalPlayer():ConCommand("requestjoin \""..v)
							net.Start( "RequestJoin" )
							net.WriteString(v)
							net.SendToServer()
							PartyFrame:Close()
						end
					end, true)
				end
			end
		end
	end, true)
	
	Partylist:NewItem(party.language["Leave Party"] , Color(200,75,75),function()
		PartyFrame:SetSize( party.framesizex, party.framesizey ) 
		PartyFrame:ShowCloseButton(false)
		PartyFrame:Configured()
		PartyFrame:ShowCloseButton(true)
		if Partytab then
			Partytab:Remove()
		end
		Partytab = vgui.Create("DPanel", PartyFrame)
		Partytab:SetSize( party.rightsize )
		Partytab:Dock(LEFT)
		
		local DLabel = vgui.Create( "PartyLabel", Partytab )
			DLabel:SetColor(Color( 0, 40 , 0 ))
			DLabel:SetText( party.language["WARNING!"] )
			DLabel:SizeToContents()
			DLabel:Center()
			DLabel:AlignTop(30)
		local DLabel = vgui.Create( "PartyLabel", Partytab )
			DLabel:SetColor(Color( 0, 40 , 0 ))
			DLabel:SetText( party.language["By leaving your party"] )
			DLabel:SizeToContents()
			DLabel:Center()
			DLabel:AlignTop(45)	
		local DLabel = vgui.Create( "PartyLabel", Partytab )
			DLabel:SetColor(Color( 0, 40 , 0 ))
			DLabel:SetText( party.language["you will no longer be protected"] )
			DLabel:SizeToContents()
			DLabel:Center()
			DLabel:AlignTop(60)			
		local DLabel = vgui.Create( "PartyLabel", Partytab )
			DLabel:SetColor(Color( 0, 40 , 0 ))
			DLabel:SetText( party.language["from damage from"] )
			DLabel:SizeToContents()
			DLabel:Center()
			DLabel:AlignTop(75)	
		local DLabel = vgui.Create( "PartyLabel", Partytab )
			DLabel:SetColor(Color( 0, 40 , 0 ))
			DLabel:SetText( party.language["your former party members."] )
			DLabel:SizeToContents()
			DLabel:Center()
			DLabel:AlignTop(90)	
			
		local DButtonStart = vgui.Create( "PartyButton")
		DButtonStart:SetParent( Partytab )
		DButtonStart:SetText( party.language["Leave Current Party" ]) 
		DButtonStart:SetSize( Partytab:GetSize()+2, 50 )
		DButtonStart:SetPos( 0, 0 ) 
		DButtonStart:SetTextColor(Color( 255,255,255) )
		DButtonStart:Dock(BOTTOM)
		DButtonStart:DockMargin(-1,0,0,-1)
		DButtonStart.DoClick = function()
			--LocalPlayer():ConCommand("LeaveParty")
			net.Start( "LeaveParty" )
			--net.WriteString(v)
			net.SendToServer()
			PartyFrame:Close()
		end
	end, true)
	for v, k in pairs(parties) do
		if table.HasValue(parties[v].members, LocalPlayer():SteamID64()) then
			-- Herkes "Parti Yönet" menüsüne erişebilir, ama içerik yetkiye göre değişir
			if table.HasValue(parties[v].members, LocalPlayer():SteamID64()) then
				Partylist:NewItem(party.language["Manage Party"] , Color(200,100,0),function()
					if Partytab then
						Partytab:Remove()
					end
					PartyFrame:SetSize( party.framesizex, party.framesizey ) 
					PartyFrame:ShowCloseButton(false)
					PartyFrame:Configured()
					PartyFrame:ShowCloseButton(true)
					Partytab = vgui.Create("PartyCategories", PartyFrame)
					Partytab:SetSize( party.rightsize )
					Partytab:Dock(LEFT)

					-- Rank Yönetimi (Lider veya createRank yetkisi olan için)
					local canManageRanks = false
					if LocalPlayer():SteamID64() == LocalPlayer():GetParty() then
						canManageRanks = true -- Lider her zaman yapabilir
					elseif LocalPlayer().HasPartyPermission and LocalPlayer():HasPartyPermission("createRank") then
						canManageRanks = true -- createRank yetkisi varsa
					end

					if canManageRanks then
						Partytab:NewItem(party.language["Ranks"], Color(150,75,200), function()
							PartyFrame:SetSize(party.framesizex + 150 , party.framesizey)
							PartyFrame:ShowCloseButton(false)
							PartyFrame:Configured()
							PartyFrame:ShowCloseButton(true)
							if Partymembs then
								Partymembs:Remove()
							end
							Partymembs = vgui.Create("DPanel", PartyFrame)
							Partymembs:SetSize(150, party.framesizey - 20)
							Partymembs:Dock(RIGHT)

							local DButton = vgui.Create( "PartyButton", Partymembs )
							DButton:SetPos( party.framesizex - 20, 0 )
							DButton:SetText( party.language["Ranks"]  )
							DButton:SetSize( 20, 20 )
							DButton:SetTextColor(Color( 255,255,255) )
							DButton:Dock(TOP)
							DButton:SetTextColor(Color(0,0,0))
							DButton.DoClick = function()
							end

							DButton.Paint = function()
								surface.DrawRect(0,0,DButton:GetWide(),DButton:GetTall())
								surface.SetDrawColor(Color(26,26,26))
								surface.DrawOutlinedRect(0,0,DButton:GetWide(),DButton:GetTall())
							end

							local Partymembslist = vgui.Create("PartyCategories", Partymembs)
							Partymembslist:SetSize(150, party.framesizey - 45)
							Partymembslist:Dock(TOP)

							-- Mevcut rütbeleri listele
							if LocalPlayer():GetParty() and parties[LocalPlayer():GetParty()] and parties[LocalPlayer():GetParty()].ranks then
								for rankID, rankData in pairs(parties[LocalPlayer():GetParty()].ranks) do
									if rankID != "leader" then -- Leader rütbesi düzenlenemez
										-- Rank düzenleme yetkisi kontrolü
										local canEditRank = false
										if LocalPlayer():SteamID64() == LocalPlayer():GetParty() then
											canEditRank = true -- Lider her zaman yapabilir
										elseif LocalPlayer().HasPartyPermission and LocalPlayer():HasPartyPermission("createRank") then
											canEditRank = true -- createRank yetkisi varsa düzenleme de yapabilir
										end

										if canEditRank then
											Partymembslist:NewItem(rankData.name, rankData.color, function()
												if OpenRankEditMenu then
													OpenRankEditMenu(rankID, rankData)
												else
													chat.AddText(Color(255, 100, 100), "Rank düzenleme menüsü henüz yüklenmedi!")
												end
											end, true)
										else
											-- Sadece görüntüleme (tıklanamaz)
											Partymembslist:NewItem(rankData.name .. " (Görüntüleme)", rankData.color, function()
												chat.AddText(Color(255, 100, 100), "Bu rütbeyi düzenleme yetkiniz yok!")
											end, false)
										end
									end
								end
							end

							-- Yeni rütbe oluştur butonu (sadece createRank yetkisi varsa)
							local canCreateRank = false
							if LocalPlayer():SteamID64() == LocalPlayer():GetParty() then
								canCreateRank = true -- Lider her zaman yapabilir
							elseif LocalPlayer().HasPartyPermission and LocalPlayer():HasPartyPermission("createRank") then
								canCreateRank = true -- createRank yetkisi varsa
							end

							if canCreateRank then
								local DButtonCreate = vgui.Create( "PartyButton", Partymembs )
								DButtonCreate:SetPos( party.framesizex - 20, 0 )
								DButtonCreate:SetText( party.language["Create Rank"]  )
								DButtonCreate:SetSize( 20, 20 )
								DButtonCreate:SetTextColor(Color( 255,255,255) )
								DButtonCreate:Dock(BOTTOM)
								DButtonCreate:DockMargin(-1,0,-1,-1)
								DButtonCreate.DoClick = function()
									if OpenRankCreateMenu then
										OpenRankCreateMenu()
									else
										chat.AddText(Color(255, 100, 100), "Rank oluşturma menüsü henüz yüklenmedi!")
									end
								end
							end
						end,true)

						-- Üye Yönetimi (Lider veya promote/demote yetkisi olan için)
						local canManageMembers = false
						if LocalPlayer():SteamID64() == LocalPlayer():GetParty() then
							canManageMembers = true -- Lider her zaman yapabilir
						elseif LocalPlayer().HasPartyPermission then
							if LocalPlayer():HasPartyPermission("promote") or LocalPlayer():HasPartyPermission("demote") then
								canManageMembers = true -- promote veya demote yetkisi varsa
							end
						end

						if canManageMembers then
							Partytab:NewItem(party.language["Member Management"], Color(200,150,75), function()
								if OpenMemberManagementMenu then
									OpenMemberManagementMenu()
								else
									chat.AddText(Color(255, 100, 100), "Üye yönetimi menüsü henüz yüklenmedi!")
								end
							end,true)
						end
					end

					-- Kick yetkisi kontrolü
					local canKick = false
					if LocalPlayer():SteamID64() == LocalPlayer():GetParty() then
						canKick = true -- Lider her zaman yapabilir
					elseif LocalPlayer().HasPartyPermission and LocalPlayer():HasPartyPermission("kick") then
						canKick = true -- kick yetkisi varsa
					end

					if canKick then
						Partytab:NewItem(party.language["Kick From Party"], Color(75,200,75), function()
						PartyFrame:SetSize(party.framesizex + 150 , party.framesizey)
						PartyFrame:ShowCloseButton(false)
						PartyFrame:Configured()
						PartyFrame:ShowCloseButton(true)
						if Partymembs then
							Partymembs:Remove()
						end
						Partymembs = vgui.Create("DPanel", PartyFrame) 
						Partymembs:SetSize(150, party.framesizey - 20 )
						Partymembs:Dock(RIGHT)
						
						local DButton = vgui.Create( "PartyButton", Partymembs )
						DButton:SetPos( party.framesizex - 20, 0 ) 
						DButton:SetText( party.language["Members"]  )
						DButton:SetSize( 20, 20 )
						DButton:SetTextColor(Color( 255,255,255) )
						DButton:Dock(TOP)
						DButton:SetTextColor(Color(0,0,0))
						DButton.DoClick = function()
						end
					
						DButton.Paint = function()
							surface.DrawRect(0,0,DButton:GetWide(),DButton:GetTall())
							surface.SetDrawColor(Color(26,26,26))
							surface.DrawOutlinedRect(0,0,DButton:GetWide(),DButton:GetTall())
						end
						local Partymembslist = vgui.Create("PartyCategories", Partymembs) 
						
						Partymembslist:SetSize(150, party.framesizey - 45)
						Partymembslist:Dock(TOP)
						
						local DButtonkick
						if LocalPlayer():GetParty() != nil then
							for v,k in pairs(parties[LocalPlayer():GetParty()].members) do
								if k != LocalPlayer():SteamID64() then
									if player.GetBySteamID64(k) !=  false then
										Partymembslist:NewItem(player.GetBySteamID64(k):Nick(), Color(255,100,100,255), function()
											if DButtonkick then
												DButtonkick:Remove()
											end
											DButtonkick = vgui.Create( "PartyButton", Partymembs )
												DButtonkick:SetPos( party.framesizex - 20, 0 ) 
												DButtonkick:SetText( party.language["Kick From Party"]  )
												DButtonkick:SetSize( 20, 20 )
												DButtonkick:SetTextColor(Color( 255,255,255) )
												DButtonkick:Dock(BOTTOM)
												DButtonkick:DockMargin(-1,0,-1,-1)
												DButtonkick.DoClick = function()
													Partymembs:Remove()
													--LocalPlayer():ConCommand("kickfromparty \""..k)
														net.Start( "KickFromParty" )
														net.WriteString(k)
														net.SendToServer()
													PartyFrame:SetSize( party.framesizex, party.framesizey )
													PartyFrame:ShowCloseButton(false)
													PartyFrame:Configured()
													PartyFrame:ShowCloseButton(true)
												end
										end, true)
									else
										Partymembslist:NewItem(party.language["offline"], Color(255,255,255,255), function()
											if DButtonkick then
												DButtonkick:Remove()
											end
											DButtonkick = vgui.Create( "PartyButton", Partymembs )
												DButtonkick:SetPos( party.framesizex - 20, 0 ) 
												DButtonkick:SetText( party.language["Kick From Party"]  )
												DButtonkick:SetSize( 20, 20 )
												DButtonkick:SetTextColor(Color( 255,255,255) )
												DButtonkick:Dock(BOTTOM)
												DButtonkick:DockMargin(-1,0,-1,-1)
												DButtonkick.DoClick = function()
													Partymembs:Remove()
													--LocalPlayer():ConCommand("kickfromparty \""..k)
														net.Start( "KickFromParty" )
														net.WriteString(k)
														net.SendToServer()
													PartyFrame:SetSize( party.framesizex, party.framesizey )
													PartyFrame:ShowCloseButton(false)
													PartyFrame:Configured()
													PartyFrame:ShowCloseButton(true)
												end
										end, true)									
									end
								end
							end
						end
					end,true)
					end -- Kick yetkisi kontrolü sonu

					-- Invite yetkisi kontrolü
					local canInvite = false
					if LocalPlayer():SteamID64() == LocalPlayer():GetParty() then
						canInvite = true -- Lider her zaman yapabilir
					elseif LocalPlayer().HasPartyPermission and LocalPlayer():HasPartyPermission("invite") then
						canInvite = true -- invite yetkisi varsa
					end

					if canInvite then
						Partytab:NewItem(party.language["Invite To Party"], Color(75,200,75), function()
						PartyFrame:SetSize(party.framesizex + 150 , party.framesizey)
						PartyFrame:ShowCloseButton(false)
						PartyFrame:Configured()
						PartyFrame:ShowCloseButton(true)
						if Partymembs then
							Partymembs:Remove()
						end
						Partymembs = vgui.Create("DPanel", PartyFrame)
						Partymembs:SetSize(150, party.framesizey - 20)
						Partymembs:Dock(RIGHT)

						local DButton = vgui.Create( "PartyButton", Partymembs )
						DButton:SetPos( party.framesizex - 20, 0 )
						DButton:SetText( party.language["Players"]  )
						DButton:SetSize( 20, 20 )
						DButton:SetTextColor(Color( 255,255,255) )
						DButton:Dock(TOP)
						DButton:SetTextColor(Color(0,0,0))
						DButton.DoClick = function()
						end

						DButton.Paint = function()
							surface.DrawRect(0,0,DButton:GetWide(),DButton:GetTall())
							surface.SetDrawColor(Color(26,26,26))
							surface.DrawOutlinedRect(0,0,DButton:GetWide(),DButton:GetTall())
						end
						local Partymembslist = vgui.Create("PartyCategories", Partymembs)

						Partymembslist:SetSize(150, party.framesizey - 45)
						Partymembslist:Dock(TOP)

						local DButtoninvite
						for v,k in ipairs(player.GetAll()) do
							if k != LocalPlayer() and !table.HasValue(parties[LocalPlayer():SteamID64()].members, k:SteamID64())then
								Partymembslist:NewItem(k:Nick(), Color(75,200,75,255), function()
									if DButtoninvite then
										DButtoninvite:Remove()
									end
									DButtoninvite = vgui.Create( "PartyButton", Partymembs )
										DButtoninvite:SetPos( party.framesizex - 20, 0 )
										DButtoninvite:SetText( party.language["Invite To Party"]  )
										DButtoninvite:SetSize( 20, 20 )
										DButtoninvite:SetTextColor(Color( 255,255,255) )
										DButtoninvite:Dock(BOTTOM)
										DButtoninvite:DockMargin(-1,0,-1,-1)
										DButtoninvite.DoClick = function()
											--LocalPlayer():ConCommand("partyinvite \""..k:SteamID64())
											net.Start( "PartyInvite" )
											net.WriteString(k:SteamID64())
											net.SendToServer()
										end
								end, true)
							end
						end
					end,true)
					end -- Invite yetkisi kontrolü sonu
				end, true)
			end
		end
	end
	Partylist:NewItem(party.language["Settings"] , Color(200,75,200),function()
		PartyFrame:SetSize( party.framesizex, party.framesizey ) 
		PartyFrame:ShowCloseButton(false)
		PartyFrame:Configured()
		PartyFrame:ShowCloseButton(true)
		if Partytab then
			Partytab:Remove()
		end
		Partytab = vgui.Create("DPanel", PartyFrame)
		Partytab:SetSize( party.rightsize )
		Partytab:Dock(LEFT)
		
		
		local DLabel = vgui.Create( "PartyLabel", Partytab )
		DLabel:SetColor(Color( 0, 40 , 0 ))
		DLabel:SetText( party.language["Color of party halo"] )
		DLabel:SizeToContents()
		DLabel:Center()
		DLabel:AlignTop(5)
		
		local Partycol = vgui.Create("DPanel", Partytab)
		Partycol:SetPos( 5, 180 )
		Partycol:SetSize( 190, 20 )
		Partycol:SetPaintBackgroundEnabled( true )
		Partycol:SetBackgroundColor( hcol )
		
		local color_picker = vgui.Create( "DRGBPicker", Partytab )
		color_picker:SetPos( 5, 20 )
		color_picker:SetSize( 30, 155 )
		
	local color_cube = vgui.Create( "DColorCube", Partytab )
		color_cube:SetPos( 40, 20 )
		color_cube:SetSize( 155, 155 )
		color_cube:SetColor( hcol )
		
	function color_picker:OnChange( col )

	local h = ColorToHSV( col )
	local _, s, v = ColorToHSV( color_cube:GetRGB() )

	col = HSVToColor( h, s, v )
	color_cube:SetColor( col )
	HaloColorUpdate()
	Partycol:SetBackgroundColor( col )
	end

	function color_cube:OnUserChanged( col )
	hcol = col
	HaloColorUpdate()
	Partycol:SetBackgroundColor( col )
	end
		
	local DermaCheckbox = vgui.Create( "DCheckBox", Partytab )
	DermaCheckbox:SetPos( 5, 205 )
	DermaCheckbox:SetValue( GetConVar( "party_lowend" ):GetInt() )	
	function DermaCheckbox:OnChange(value)
		if value then
		GetConVar( "party_lowend" ):SetInt(1)
		else 
		GetConVar( "party_lowend" ):SetInt(0)
		end
	end
		local DLabel2 = vgui.Create( "PartyLabel", Partytab )
		DLabel2:SetColor(Color( 0, 40 , 0 ))
		DLabel2:SetText( party.language["Lowend Halo"] )
		DLabel2:SizeToContents()
		DLabel2:AlignTop(205)
		DLabel2:AlignLeft(25)
		
	local DermaCheckbox2 = vgui.Create( "DCheckBox", Partytab )
		DermaCheckbox2:SetPos( 5, 225 )
		DermaCheckbox2:SetValue( GetConVar( "party_showhud" ):GetInt() )	
		function DermaCheckbox2:OnChange(value)
			if value then
				GetConVar( "party_showhud" ):SetInt(1)
			else 
				GetConVar( "party_showhud" ):SetInt(0)
			end
		end
		local DLabel3 = vgui.Create( "PartyLabel", Partytab )
		DLabel3:SetColor(Color( 0, 40 , 0 ))
		DLabel3:SetText( party.language["Disable Hud?"] )
		DLabel3:SizeToContents()
		DLabel3:AlignTop(225)
		DLabel3:AlignLeft(25)
		
		
		
		
	end, true)
	
	if LocalPlayer():IsAdmin() then
		Partylist:NewItem(party.language["Admin"] , Color(200,0,0),function()
			PartyFrame:SetSize( party.framesizex, party.framesizey ) 
			PartyFrame:ShowCloseButton(false)
			PartyFrame:Configured()
			PartyFrame:ShowCloseButton(true)
			if Partytab then
				Partytab:Remove()
			end
			Partytab = vgui.Create("PartyCategories", PartyFrame)
			Partytab:SetSize( party.rightsize )
			Partytab:Dock(LEFT)
			
			
			Partytab:NewItem(party.language["Kick From Party"], Color(255,0,0), function()
				PartyFrame:SetSize(party.framesizex + 150 , party.framesizey)
				PartyFrame:ShowCloseButton(false)
				PartyFrame:Configured()
				PartyFrame:ShowCloseButton(true)
				if Partymembs then
					Partymembs:Remove()
				end
				Partymembs = vgui.Create("DPanel", PartyFrame) 
				Partymembs:SetSize(150, party.framesizey - 40)
				Partymembs:Dock(RIGHT)
				
				local DButton = vgui.Create( "PartyButton", Partymembs )
				DButton:SetPos( party.framesizex - 20, 0 ) 
				DButton:SetText( party.language["Members"]  )
				DButton:SetSize( 20, 20 )
				DButton:SetTextColor(Color( 255,255,255) )
				DButton:Dock(TOP)
				DButton:SetTextColor(Color(0,0,0))
				DButton.DoClick = function()
				end
			
				DButton.Paint = function()
					surface.DrawRect(0,0,DButton:GetWide(),DButton:GetTall())
					surface.SetDrawColor(Color(26,26,26))
					surface.DrawOutlinedRect(0,0,DButton:GetWide(),DButton:GetTall())
				end
				local Partymembslist = vgui.Create("PartyCategories", Partymembs) 
				
				Partymembslist:SetSize(150, party.framesizey - 45)
				Partymembslist:Dock(TOP)
				
				local DButtonkick
				for v,k in ipairs(player.GetAll()) do
					for t,z in pairs(parties) do
						if table.HasValue(parties[t].members, k:SteamID64()) then
							Partymembslist:NewItem(k:Nick(), Color(255,0,0,255), function()
								if DButtonkick then
									DButtonkick:Remove()
								end
								DButtonkick = vgui.Create( "PartyButton", Partymembs )
								DButtonkick:SetPos( party.framesizex - 20, 0 ) 
								DButtonkick:SetText( party.language["Kick From Party"]  )
								DButtonkick:SetSize( 20, 20 )
								DButtonkick:SetTextColor(Color( 255,255,255) )
								DButtonkick:Dock(BOTTOM)
								DButtonkick:DockMargin(-1,0,-1,-1)
								DButtonkick.DoClick = function()
									Partymembs:Remove()
									--LocalPlayer():ConCommand("kickfromparty \""..k:SteamID64())
									net.Start( "KickFromParty" )
									net.WriteString(k:SteamID64())
									net.SendToServer()
									PartyFrame:SetSize( party.framesizex, party.framesizey )
									PartyFrame:ShowCloseButton(false)
									PartyFrame:Configured()
									PartyFrame:ShowCloseButton(true)
								end
							end, true)
						end
					end
				end
			end,true)
			
			Partytab:NewItem(party.language["Disband Party"], Color(255,0,0), function()
				PartyFrame:SetSize(party.framesizex + 150 , party.framesizey)
				PartyFrame:ShowCloseButton(false)
				PartyFrame:Configured()
				PartyFrame:ShowCloseButton(true)
				if Partymembs then
					Partymembs:Remove()
				end
				Partymembs = vgui.Create("DPanel", PartyFrame) 
				Partymembs:SetSize(150, party.framesizey - 20)
				Partymembs:Dock(RIGHT)
				
				local DButton = vgui.Create( "PartyButton", Partymembs )
				DButton:SetPos( party.framesizex - 20, 0 ) 
				DButton:SetText( party.language["Parties"]  )
				DButton:SetSize( 20, 20 )
				DButton:SetTextColor(Color( 255,255,255) )
				DButton:Dock(TOP)
				DButton:SetTextColor(Color(0,0,0))
				DButton.DoClick = function()
				end
			
				DButton.Paint = function()
					surface.DrawRect(0,0,DButton:GetWide(),DButton:GetTall())
					surface.SetDrawColor(Color(26,26,26))
					surface.DrawOutlinedRect(0,0,DButton:GetWide(),DButton:GetTall())
				end
				local Partymembslist = vgui.Create("PartyCategories", Partymembs) 
				
				Partymembslist:SetSize(150, party.framesizey - 45)
				Partymembslist:Dock(TOP)
				
				local DButtondsban
				for v,k in pairs(parties) do
					Partymembslist:NewItem(parties[v].name, Color(255,0,0,255), function()
						if DButtondsban then
							DButtondsban:Remove()
						end
						DButtondsban = vgui.Create( "PartyButton", Partymembs )
							DButtondsban:SetPos( party.framesizex - 20, 0 ) 
							DButtondsban:SetText( party.language["Disband Party"] )
							DButtondsban:SetSize( 20, 20 )
							DButtondsban:SetTextColor(Color( 255,255,255) )
							DButtondsban:Dock(BOTTOM)
							DButtondsban:DockMargin(-1,0,-1,-1)
							DButtondsban.DoClick = function()
								Partymembs:Remove()
								--LocalPlayer():ConCommand("disbandparty \""..v)
									net.Start( "DisbandParty" )
									net.WriteString(v)
									net.SendToServer()
								PartyFrame:SetSize( party.framesizex, party.framesizey )
								PartyFrame:ShowCloseButton(false)
								PartyFrame:Configured()
								PartyFrame:ShowCloseButton(true)
							end
					end, true)
				end
			end,true)
		end, true)
	end
end

-- Rank oluşturma menüsü
function OpenRankCreateMenu()
	local Frame = vgui.Create("DFrame")
	Frame:SetSize(400, 500)
	Frame:Center()
	Frame:SetTitle(party.language["Create Rank"])
	Frame:MakePopup()
	Frame:SetDeleteOnClose(true)

	function Frame:Paint(w, h)
		draw.RoundedBox(5, 0, 0, w, h, party.backgroundcolor)
	end

	-- Rank ID
	local rankIDLabel = vgui.Create("DLabel", Frame)
	rankIDLabel:SetText("Rank ID:")
	rankIDLabel:SetPos(10, 30)
	rankIDLabel:SizeToContents()

	local rankIDEntry = vgui.Create("DTextEntry", Frame)
	rankIDEntry:SetPos(10, 50)
	rankIDEntry:SetSize(380, 25)

	-- Rank Adı
	local rankNameLabel = vgui.Create("DLabel", Frame)
	rankNameLabel:SetText(party.language["Rank Name"] .. ":")
	rankNameLabel:SetPos(10, 80)
	rankNameLabel:SizeToContents()

	local rankNameEntry = vgui.Create("DTextEntry", Frame)
	rankNameEntry:SetPos(10, 100)
	rankNameEntry:SetSize(380, 25)

	-- Rank Rengi
	local rankColorLabel = vgui.Create("DLabel", Frame)
	rankColorLabel:SetText(party.language["Rank Color"] .. ":")
	rankColorLabel:SetPos(10, 130)
	rankColorLabel:SizeToContents()

	local selectedColor = Color(255, 255, 255, 255)

	local colorPicker = vgui.Create("DColorMixer", Frame)
	colorPicker:SetPos(10, 150)
	colorPicker:SetSize(200, 150)
	colorPicker:SetPalette(true)
	colorPicker:SetAlphaBar(false)
	colorPicker:SetWangs(true)
	colorPicker:SetColor(selectedColor)

	function colorPicker:ValueChanged(col)
		selectedColor = col
	end

	-- İzinler
	local permissionsLabel = vgui.Create("DLabel", Frame)
	permissionsLabel:SetText(party.language["Permissions"] .. ":")
	permissionsLabel:SetPos(220, 150)
	permissionsLabel:SizeToContents()

	local permissions = {}
	local permissionCheckboxes = {}

	local permissionList = {
		{id = "kick", name = "Kick"},
		{id = "invite", name = "Invite"},
		{id = "promote", name = party.language["Promote"]},
		{id = "demote", name = party.language["Demote"]},
		{id = "createRank", name = party.language["Create Rank"]},
		{id = "transferLeadership", name = party.language["Transfer Leadership"]},
		{id = "disbandParty", name = party.language["Disband Party"]}
	}

	for i, perm in ipairs(permissionList) do
		local checkbox = vgui.Create("DCheckBox", Frame)
		checkbox:SetPos(220, 170 + (i-1) * 25)
		checkbox:SetSize(20, 20)
		checkbox:SetValue(false)

		local label = vgui.Create("DLabel", Frame)
		label:SetText(perm.name)
		label:SetPos(245, 170 + (i-1) * 25)
		label:SizeToContents()

		function checkbox:OnChange(val)
			permissions[perm.id] = val
		end

		permissionCheckboxes[perm.id] = checkbox
	end

	-- Oluştur butonu
	local createButton = vgui.Create("DButton", Frame)
	createButton:SetPos(10, 450)
	createButton:SetSize(180, 30)
	createButton:SetText(party.language["Create Rank"])

	function createButton:DoClick()
		local rankID = rankIDEntry:GetValue()
		local rankName = rankNameEntry:GetValue()

		if rankID == "" or rankName == "" then
			chat.AddText(Color(255, 0, 0), "Rank ID ve isim boş olamaz!")
			return
		end

		net.Start("CreateRank")
		net.WriteString(rankID)
		net.WriteString(rankName)
		net.WriteColor(selectedColor)
		net.WriteTable(permissions)
		net.SendToServer()

		Frame:Close()
	end

	-- İptal butonu
	local cancelButton = vgui.Create("DButton", Frame)
	cancelButton:SetPos(210, 450)
	cancelButton:SetSize(180, 30)
	cancelButton:SetText("İptal")

	function cancelButton:DoClick()
		Frame:Close()
	end
end

-- Rank düzenleme menüsü
function OpenRankEditMenu(rankID, rankData)
	local Frame = vgui.Create("DFrame")
	Frame:SetSize(500, 550)
	Frame:Center()
	Frame:SetTitle(party.language["Edit Rank"] .. ": " .. rankData.name)
	Frame:MakePopup()
	Frame:SetDeleteOnClose(true)

	function Frame:Paint(w, h)
		draw.RoundedBox(5, 0, 0, w, h, party.backgroundcolor)
	end

	-- Rank Adı
	local rankNameLabel = vgui.Create("DLabel", Frame)
	rankNameLabel:SetText(party.language["Rank Name"] .. ":")
	rankNameLabel:SetPos(10, 30)
	rankNameLabel:SizeToContents()

	local rankNameEntry = vgui.Create("DTextEntry", Frame)
	rankNameEntry:SetPos(10, 50)
	rankNameEntry:SetSize(480, 25)
	rankNameEntry:SetValue(rankData.name)

	-- Rank Rengi
	local rankColorLabel = vgui.Create("DLabel", Frame)
	rankColorLabel:SetText(party.language["Rank Color"] .. ":")
	rankColorLabel:SetPos(10, 80)
	rankColorLabel:SizeToContents()

	local selectedColor = rankData.color or Color(255, 255, 255, 255)

	local colorPicker = vgui.Create("DColorMixer", Frame)
	colorPicker:SetPos(10, 100)
	colorPicker:SetSize(200, 150)
	colorPicker:SetPalette(true)
	colorPicker:SetAlphaBar(false)
	colorPicker:SetWangs(true)
	colorPicker:SetColor(selectedColor)

	function colorPicker:ValueChanged(col)
		selectedColor = col
	end

	-- İzinler
	local permissionsLabel = vgui.Create("DLabel", Frame)
	permissionsLabel:SetText(party.language["Permissions"] .. ":")
	permissionsLabel:SetPos(220, 100)
	permissionsLabel:SizeToContents()

	local permissions = table.Copy(rankData.permissions or {})
	local permissionCheckboxes = {}

	local permissionList = {
		{id = "kick", name = "Kick"},
		{id = "invite", name = "Invite"},
		{id = "promote", name = party.language["Promote"]},
		{id = "demote", name = party.language["Demote"]},
		{id = "createRank", name = party.language["Create Rank"]},
		{id = "transferLeadership", name = party.language["Transfer Leadership"]},
		{id = "disbandParty", name = party.language["Disband Party"]}
	}

	for i, perm in ipairs(permissionList) do
		local checkbox = vgui.Create("DCheckBox", Frame)
		checkbox:SetPos(220, 120 + (i-1) * 25)
		checkbox:SetSize(20, 20)
		checkbox:SetValue(permissions[perm.id] or false)

		local label = vgui.Create("DLabel", Frame)
		label:SetText(perm.name)
		label:SetPos(245, 120 + (i-1) * 25)
		label:SizeToContents()

		function checkbox:OnChange(val)
			permissions[perm.id] = val
		end

		permissionCheckboxes[perm.id] = checkbox
	end

	-- Kaydet butonu
	local saveButton = vgui.Create("DButton", Frame)
	saveButton:SetPos(10, 450)
	saveButton:SetSize(120, 30)
	saveButton:SetText("Kaydet")

	function saveButton:DoClick()
		local rankName = rankNameEntry:GetValue()

		if rankName == "" then
			chat.AddText(Color(255, 0, 0), "Rank ismi boş olamaz!")
			return
		end

		net.Start("EditRank")
		net.WriteString(rankID)
		net.WriteString(rankName)
		net.WriteColor(selectedColor)
		net.WriteTable(permissions)
		net.SendToServer()

		Frame:Close()
	end

	-- Sil butonu
	local deleteButton = vgui.Create("DButton", Frame)
	deleteButton:SetPos(140, 450)
	deleteButton:SetSize(120, 30)
	deleteButton:SetText(party.language["Delete Rank"])

	function deleteButton:DoClick()
		local confirmFrame = vgui.Create("DFrame")
		confirmFrame:SetSize(300, 150)
		confirmFrame:Center()
		confirmFrame:SetTitle("Emin misiniz?")
		confirmFrame:MakePopup()

		local confirmLabel = vgui.Create("DLabel", confirmFrame)
		confirmLabel:SetText("Bu rütbeyi silmek istediğinizden emin misiniz?")
		confirmLabel:SetPos(10, 30)
		confirmLabel:SizeToContents()

		local yesButton = vgui.Create("DButton", confirmFrame)
		yesButton:SetPos(50, 100)
		yesButton:SetSize(80, 30)
		yesButton:SetText("Evet")

		function yesButton:DoClick()
			net.Start("DeleteRank")
			net.WriteString(rankID)
			net.SendToServer()

			confirmFrame:Close()
			Frame:Close()
		end

		local noButton = vgui.Create("DButton", confirmFrame)
		noButton:SetPos(150, 100)
		noButton:SetSize(80, 30)
		noButton:SetText("Hayır")

		function noButton:DoClick()
			confirmFrame:Close()
		end
	end

	-- İptal butonu
	local cancelButton = vgui.Create("DButton", Frame)
	cancelButton:SetPos(270, 450)
	cancelButton:SetSize(120, 30)
	cancelButton:SetText("İptal")

	function cancelButton:DoClick()
		Frame:Close()
	end
end

-- Üye yönetimi menüsü
function OpenMemberManagementMenu()
	local Frame = vgui.Create("DFrame")
	Frame:SetSize(600, 400)
	Frame:Center()
	Frame:SetTitle("Üye Yönetimi")
	Frame:MakePopup()
	Frame:SetDeleteOnClose(true)

	function Frame:Paint(w, h)
		draw.RoundedBox(5, 0, 0, w, h, party.backgroundcolor)
	end

	-- Üye listesi
	local memberList = vgui.Create("DListView", Frame)
	memberList:SetPos(10, 30)
	memberList:SetSize(380, 300)
	memberList:AddColumn("Oyuncu")
	memberList:AddColumn("Rütbe")
	memberList:AddColumn("Durum")

	-- Parti üyelerini listele
	if LocalPlayer():GetParty() and parties[LocalPlayer():GetParty()] then
		local partyData = parties[LocalPlayer():GetParty()]
		for _, memberID in pairs(partyData.members) do
			-- Liderin kendini listeye eklememesi için kontrol
			if memberID != LocalPlayer():SteamID64() then
				local ply = player.GetBySteamID64(memberID)
				local rankID = partyData.memberRanks[memberID] or "member"
				local rankName = partyData.ranks[rankID] and partyData.ranks[rankID].name or "Üye"
				local status = ply and "Çevrimiçi" or "Çevrimdışı"
				local playerName = ply and ply:Nick() or "Bilinmeyen"

				local line = memberList:AddLine(playerName, rankName, status)
				line.memberID = memberID
				line.rankID = rankID
			end
		end
	end

	-- Seçili üye için işlemler
	local selectedMember = nil

	function memberList:OnRowSelected(index, row)
		selectedMember = row
	end

	-- Rütbe değiştirme
	local rankLabel = vgui.Create("DLabel", Frame)
	rankLabel:SetText("Rütbe Değiştir:")
	rankLabel:SetPos(400, 30)
	rankLabel:SizeToContents()

	local rankCombo = vgui.Create("DComboBox", Frame)
	rankCombo:SetPos(400, 50)
	rankCombo:SetSize(180, 25)

	-- Mevcut rütbeleri combo'ya ekle
	if LocalPlayer():GetParty() and parties[LocalPlayer():GetParty()] then
		for rankID, rankData in pairs(parties[LocalPlayer():GetParty()].ranks) do
			if rankID != "leader" then -- Leader rütbesi atanamaz
				rankCombo:AddChoice(rankData.name, rankID)
			end
		end
	end

	local promoteButton = vgui.Create("DButton", Frame)
	promoteButton:SetPos(400, 80)
	promoteButton:SetSize(180, 30)
	promoteButton:SetText("Rütbe Değiştir")

	function promoteButton:DoClick()
		if not selectedMember then
			chat.AddText(Color(255, 0, 0), "Lütfen bir üye seçin!")
			return
		end

		local _, newRankID = rankCombo:GetSelected()
		if not newRankID then
			chat.AddText(Color(255, 0, 0), "Lütfen bir rütbe seçin!")
			return
		end

		net.Start("PromotePlayer")
		net.WriteString(selectedMember.memberID)
		net.WriteString(newRankID)
		net.SendToServer()

		Frame:Close()
	end

	-- Liderlik devretme
	local transferButton = vgui.Create("DButton", Frame)
	transferButton:SetPos(400, 120)
	transferButton:SetSize(180, 30)
	transferButton:SetText(party.language["Transfer Leadership"])

	function transferButton:DoClick()
		if not selectedMember then
			chat.AddText(Color(255, 0, 0), "Lütfen bir üye seçin!")
			return
		end

		if selectedMember.memberID == LocalPlayer():SteamID64() then
			chat.AddText(Color(255, 0, 0), "Kendinize liderlik devredemezsiniz!")
			return
		end

		-- Onay menüsü
		local confirmFrame = vgui.Create("DFrame")
		confirmFrame:SetSize(350, 150)
		confirmFrame:Center()
		confirmFrame:SetTitle("Liderlik Devretme Onayı")
		confirmFrame:MakePopup()

		local confirmLabel = vgui.Create("DLabel", confirmFrame)
		confirmLabel:SetText("Liderliği " .. selectedMember:GetValue(1) .. " oyuncusuna devretmek istediğinizden emin misiniz?")
		confirmLabel:SetPos(10, 30)
		confirmLabel:SetWrap(true)
		confirmLabel:SetSize(330, 50)

		local yesButton = vgui.Create("DButton", confirmFrame)
		yesButton:SetPos(50, 100)
		yesButton:SetSize(100, 30)
		yesButton:SetText("Evet, Devret")

		function yesButton:DoClick()
			net.Start("TransferLeadership")
			net.WriteString(selectedMember.memberID)
			net.SendToServer()

			confirmFrame:Close()
			Frame:Close()
		end

		local noButton = vgui.Create("DButton", confirmFrame)
		noButton:SetPos(200, 100)
		noButton:SetSize(100, 30)
		noButton:SetText("Hayır")

		function noButton:DoClick()
			confirmFrame:Close()
		end
	end

	-- Kapat butonu
	local closeButton = vgui.Create("DButton", Frame)
	closeButton:SetPos(400, 350)
	closeButton:SetSize(180, 30)
	closeButton:SetText("Kapat")

	function closeButton:DoClick()
		Frame:Close()
	end
end

-- Debug komutu - Menü erişim yetkilerini kontrol et
concommand.Add("debug_menu_permissions", function()
	local ply = LocalPlayer()
	local partyID = ply:GetParty()

	if not partyID then
		print("❌ Parti bulunamadı!")
		return
	end

	print("=== Menü Erişim Yetkileri ===")
	print("Parti ID: " .. partyID)
	print("Oyuncu Steam ID: " .. ply:SteamID64())
	print("Lider mi: " .. tostring(ply:SteamID64() == partyID))

	-- Fonksiyon kontrolü
	if ply.HasPartyPermission then
		print("✅ HasPartyPermission fonksiyonu mevcut")

		local permissions = {"createRank", "promote", "demote", "kick", "invite", "transferLeadership", "disbandParty"}
		for _, perm in ipairs(permissions) do
			local hasPerm = ply:HasPartyPermission(perm)
			print("  " .. perm .. ": " .. tostring(hasPerm))
		end
	else
		print("❌ HasPartyPermission fonksiyonu bulunamadı!")
	end

	-- Menü erişim kontrolü
	local canManageRanks = false
	if ply:SteamID64() == partyID then
		canManageRanks = true
	elseif ply.HasPartyPermission and ply:HasPartyPermission("createRank") then
		canManageRanks = true
	end

	local canManageMembers = false
	if ply:SteamID64() == partyID then
		canManageMembers = true
	elseif ply.HasPartyPermission then
		if ply:HasPartyPermission("promote") or ply:HasPartyPermission("demote") then
			canManageMembers = true
		end
	end

	local canKick = false
	if ply:SteamID64() == partyID then
		canKick = true
	elseif ply.HasPartyPermission and ply:HasPartyPermission("kick") then
		canKick = true
	end

	local canInvite = false
	if ply:SteamID64() == partyID then
		canInvite = true
	elseif ply.HasPartyPermission and ply:HasPartyPermission("invite") then
		canInvite = true
	end

	print("\n--- Menü Erişimleri ---")
	print("Parti Yönet Menüsü: ✅ (Herkes erişebilir)")
	print("Rütbeler Menüsü: " .. (canManageRanks and "✅" or "❌"))
	print("Üye Yönetimi Menüsü: " .. (canManageMembers and "✅" or "❌"))
	print("Kick Menüsü: " .. (canKick and "✅" or "❌"))
	print("Invite Menüsü: " .. (canInvite and "✅" or "❌"))
	print("========================")
end)

print("[Party System] Menü izin sistemi yüklendi:")
print("  - debug_menu_permissions: Menü erişim yetkilerini kontrol et")