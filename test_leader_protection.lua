-- Lider Koruma Sistemi Test Dosyası

if SERVER then
    concommand.Add("test_leader_protection", function(ply, cmd, args)
        if not ply:IsAdmin() then return end
        
        print("=== Lider Koruma Sistemi Test Başlıyor ===")
        
        -- Test 1: Parti oluştur
        ply:Startparty("Test Partisi")
        local partyID = ply:GetParty()
        print("✓ Parti oluşturuldu. Lider: " .. ply:<PERSON>() .. " (" .. partyID .. ")")
        
        -- Test 2: Liderin kendi rütbesini değiştirmeye çalışması
        print("\n--- Test 2: Lider kendi rütbesini değiştirmeye çalışıyor ---")
        ply:PromotePlayer(ply:SteamID64(), "member")
        -- Beklenen sonuç: "Lider kendi rütbesini değiştiremez!" mesajı
        
        -- Test 3: <PERSON><PERSON>in kendini kicklemeye çalışması
        print("\n--- Test 3: <PERSON>der kendini kicklemeye çalışıyor ---")
        ply:kickfromparty(ply:SteamID64())
        -- Beklenen sonuç: "Lider kendini partiden atamaz!" mesajı
        
        -- Test 4: Başka bir oyuncu varsa onu test et
        local testTarget = nil
        for _, p in pairs(player.GetAll()) do
            if p != ply then
                testTarget = p
                break
            end
        end
        
        if testTarget then
            print("\n--- Test 4: Normal üye işlemleri ---")
            
            -- Üyeyi partiye ekle
            testTarget:JoinParty(partyID)
            print("✓ " .. testTarget:Nick() .. " partiye eklendi")
            
            -- Üyenin rütbesini değiştir (bu çalışmalı)
            ply:PromotePlayer(testTarget:SteamID64(), "officer")
            print("✓ " .. testTarget:Nick() .. " officer rütbesine terfi ettirildi")
            
            -- Üyeyi kick et (bu çalışmalı)
            ply:kickfromparty(testTarget:SteamID64())
            print("✓ " .. testTarget:Nick() .. " partiden atıldı")
        else
            print("\n--- Test 4 atlandı: Başka oyuncu bulunamadı ---")
        end
        
        print("\n=== Test Tamamlandı ===")
        print("Lider koruma sistemi aktif!")
    end)
    
    concommand.Add("test_rank_permissions", function(ply, cmd, args)
        if not ply:IsAdmin() then return end
        
        print("=== Rank İzin Sistemi Test ===")
        
        local partyID = ply:GetParty()
        if not partyID then
            ply:Startparty("Test Partisi")
            partyID = ply:GetParty()
        end
        
        -- Test rütbesi oluştur
        ply:CreatePartyRank("test_rank", "Test Rütbesi", Color(100, 200, 100), {
            kick = false,
            invite = true,
            promote = false,
            demote = false,
            createRank = false,
            transferLeadership = false,
            disbandParty = false
        })
        
        print("✓ Test rütbesi oluşturuldu")
        
        -- Başka bir oyuncu varsa test et
        local testTarget = nil
        for _, p in pairs(player.GetAll()) do
            if p != ply then
                testTarget = p
                break
            end
        end
        
        if testTarget then
            -- Oyuncuyu partiye ekle ve test rütbesine ata
            testTarget:JoinParty(partyID)
            ply:PromotePlayer(testTarget:SteamID64(), "test_rank")
            
            print("✓ " .. testTarget:Nick() .. " test rütbesine atandı")
            
            -- Test oyuncusunun izinlerini kontrol et
            print("\nTest oyuncusunun izinleri:")
            local permissions = {
                "kick", "invite", "promote", "demote", 
                "createRank", "transferLeadership", "disbandParty"
            }
            
            for _, perm in ipairs(permissions) do
                local hasPerm = testTarget:HasPartyPermission(perm)
                print("  " .. perm .. ": " .. tostring(hasPerm))
            end
        else
            print("Başka oyuncu bulunamadı, izin testi atlandı")
        end
        
        print("\n=== Test Tamamlandı ===")
    end)
    
    print("Lider koruma test komutları yüklendi:")
    print("  - test_leader_protection: Lider koruma sistemini test et")
    print("  - test_rank_permissions: Rank izin sistemini test et")
end

if CLIENT then
    concommand.Add("test_leader_ui", function()
        print("=== Lider UI Test ===")
        
        local partyID = LocalPlayer():GetParty()
        if not partyID then
            print("Parti bulunamadı! Önce parti oluşturun.")
            return
        end
        
        local isLeader = (LocalPlayer():SteamID64() == partyID)
        print("Oyuncu parti lideri mi: " .. tostring(isLeader))
        
        if parties[partyID] then
            print("\nParti üyeleri:")
            for _, memberID in pairs(parties[partyID].members) do
                local ply = player.GetBySteamID64(memberID)
                local name = ply and ply:Nick() or "Offline"
                local isCurrentPlayer = (memberID == LocalPlayer():SteamID64())
                print("  - " .. name .. " (" .. memberID .. ")" .. (isCurrentPlayer and " [SEN]" or ""))
            end
            
            print("\nÜye yönetimi menüsünde görünecek oyuncular:")
            for _, memberID in pairs(parties[partyID].members) do
                if memberID != LocalPlayer():SteamID64() then
                    local ply = player.GetBySteamID64(memberID)
                    local name = ply and ply:Nick() or "Offline"
                    print("  - " .. name .. " (Düzenlenebilir)")
                end
            end
        end
        
        print("\n=== Test Tamamlandı ===")
    end)
    
    print("Client lider test komutu yüklendi: test_leader_ui")
end
