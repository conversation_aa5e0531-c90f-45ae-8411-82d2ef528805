-- Parti HUD Test Dosyası

if CLIENT then
    concommand.Add("test_hud_ranks", function()
        print("=== Parti HUD Rank Test ===")
        
        local ply = LocalPlayer()
        local partyID = ply:GetParty()
        
        if not partyID then
            print("❌ Parti bulunamadı! Önce parti oluşturun.")
            return
        end
        
        print("✅ Parti ID: " .. partyID)
        print("✅ Oyuncu Steam ID: " .. ply:SteamID64())
        print("✅ Lider mi: " .. tostring(ply:SteamID64() == partyID))
        
        -- Rank fonksiyonlarını test et
        if ply.GetPartyRankColor then
            local rankColor = ply:GetPartyRankColor()
            print("✅ Rank Rengi: " .. rankColor.r .. "," .. rankColor.g .. "," .. rankColor.b)
        else
            print("❌ GetPartyRankColor fonksiyonu bulunamadı!")
        end
        
        if ply.GetPartyRankName then
            local rankName = ply:GetPartyRankName()
            print("✅ Rank Adı: " .. rankName)
        else
            print("❌ GetPartyRankName fonksiyonu bulunamadı!")
        end
        
        -- Parti verilerini kontrol et
        if parties[partyID] then
            print("\n--- Parti Verileri ---")
            print("Parti Adı: " .. parties[partyID].name)
            
            if parties[partyID].memberRanks then
                print("Üye Rütbeleri:")
                for memberID, rankID in pairs(parties[partyID].memberRanks) do
                    local member = player.GetBySteamID64(memberID)
                    local memberName = member and member:Nick() or "Offline"
                    print("  " .. memberName .. " (" .. memberID .. ") -> " .. rankID)
                end
            end
            
            if parties[partyID].ranks then
                print("Mevcut Rütbeler:")
                for rankID, rankData in pairs(parties[partyID].ranks) do
                    local color = rankData.color
                    print("  " .. rankID .. ": " .. rankData.name .. " (R:" .. color.r .. " G:" .. color.g .. " B:" .. color.b .. ")")
                end
            end
        else
            print("❌ Parti verileri bulunamadı!")
        end
        
        print("\n=== Test Tamamlandı ===")
    end)
    
    concommand.Add("test_hud_colors", function()
        print("=== HUD Renk Test ===")
        
        local testColors = {
            {name = "Kırmızı", color = Color(255, 100, 100)},
            {name = "Mavi", color = Color(100, 150, 255)},
            {name = "Yeşil", color = Color(100, 255, 100)},
            {name = "Sarı", color = Color(255, 255, 100)},
            {name = "Mor", color = Color(200, 100, 255)},
        }
        
        for i, testColor in ipairs(testColors) do
            print(testColor.name .. " rengi: R:" .. testColor.color.r .. " G:" .. testColor.color.g .. " B:" .. testColor.color.b)
        end
        
        print("Bu renkler HUD'da test edilebilir.")
    end)
    
    concommand.Add("toggle_party_hud", function()
        local currentValue = GetConVar("party_showhud"):GetInt()
        local newValue = currentValue == 1 and 0 or 1
        
        GetConVar("party_showhud"):SetInt(newValue)
        
        if newValue == 1 then
            print("✅ Parti HUD gizlendi")
        else
            print("✅ Parti HUD gösteriliyor")
        end
    end)
    
    -- HUD pozisyon ayarlama
    concommand.Add("set_hud_position", function(ply, cmd, args)
        local x = tonumber(args[1]) or 50
        local y = tonumber(args[2]) or 50
        
        party.hudhorizontalpos = math.Clamp(x, 0, ScrW() - 200)
        party.hudverticalpos = math.Clamp(y, 0, ScrH() - 300)
        
        GetConVar("party.hudhorizontalpos"):SetInt(party.hudhorizontalpos)
        GetConVar("party.hudverticalpos"):SetInt(party.hudverticalpos)
        
        print("✅ HUD pozisyonu ayarlandı: X=" .. party.hudhorizontalpos .. " Y=" .. party.hudverticalpos)
    end)
    
    print("Parti HUD test komutları yüklendi:")
    print("  - test_hud_ranks: Rank bilgilerini test et")
    print("  - test_hud_colors: Renk örneklerini göster")
    print("  - toggle_party_hud: HUD'ı aç/kapat")
    print("  - set_hud_position <x> <y>: HUD pozisyonunu ayarla")
end

if SERVER then
    concommand.Add("create_colorful_ranks", function(ply, cmd, args)
        if not ply:IsAdmin() then return end
        
        local partyID = ply:GetParty()
        if not partyID then
            ply:Startparty("Renkli Parti")
            partyID = ply:GetParty()
        end
        
        -- Renkli test rütbeleri oluştur
        local testRanks = {
            {id = "vip", name = "VIP", color = Color(255, 215, 0), permissions = {invite = true}},
            {id = "moderator", name = "Moderatör", color = Color(100, 150, 255), permissions = {kick = true, invite = true}},
            {id = "trusted", name = "Güvenilir", color = Color(100, 255, 100), permissions = {invite = true}},
            {id = "newbie", name = "Yeni", color = Color(255, 150, 150), permissions = {}},
        }
        
        for _, rank in ipairs(testRanks) do
            ply:CreatePartyRank(rank.id, rank.name, rank.color, rank.permissions)
            print("✅ " .. rank.name .. " rütbesi oluşturuldu")
        end
        
        print("🎨 Renkli test rütbeleri oluşturuldu!")
        print("HUD'da farklı renklerde görünecekler.")
    end)
    
    concommand.Add("assign_test_ranks", function(ply, cmd, args)
        if not ply:IsAdmin() then return end
        
        local partyID = ply:GetParty()
        if not partyID then
            print("❌ Parti bulunamadı!")
            return
        end
        
        local availableRanks = {"vip", "moderator", "trusted", "newbie", "officer", "member"}
        local rankIndex = 1
        
        -- Parti üyelerine farklı rütbeler ata
        for _, memberID in pairs(parties[partyID].members) do
            if memberID != ply:SteamID64() then -- Lideri atla
                local rankID = availableRanks[rankIndex] or "member"
                ply:PromotePlayer(memberID, rankID)
                
                local member = player.GetBySteamID64(memberID)
                local memberName = member and member:Nick() or "Offline"
                print("✅ " .. memberName .. " -> " .. rankID)
                
                rankIndex = rankIndex + 1
                if rankIndex > #availableRanks then
                    rankIndex = 1
                end
            end
        end
        
        print("🎯 Test rütbeleri atandı! HUD'da farklı renkler göreceksiniz.")
    end)
    
    print("Sunucu HUD test komutları yüklendi:")
    print("  - create_colorful_ranks: Renkli test rütbeleri oluştur")
    print("  - assign_test_ranks: Üyelere test rütbeleri ata")
end
