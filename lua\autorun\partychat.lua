if SERVER then
	util.AddNetworkString( "PartyChat")
	local function SendPartyMessage(ply, text)
		local Chatmembers = {}
		if !ply:GetParty() then return end
		for v, k in pairs (parties[ply:GetParty()].members) do
			table.insert(Chatmembers , player.GetBySteamID64(k))
		end
		local chattext = string.Right(text, string.len(text) - string.len( party.partychatcommand) - 1)

		-- Rank bilgilerini al
		local playerName = ply:Nick()
		local rankName = ""
		local rankColor = Color(255, 255, 255)

		local partyID = ply:GetParty()
		if parties[partyID] and parties[partyID].memberRanks then
			local rankID = parties[partyID].memberRanks[ply:SteamID64()] or "member"
			if parties[partyID].ranks and parties[partyID].ranks[rankID] then
				rankName = parties[partyID].ranks[rankID].name or ""
				rankColor = parties[partyID].ranks[rankID].color or Color(255, 255, 255)
			end
		end

		-- Chat tablosunu rank bilgileriyle oluştur
		local chattable = {playerName, chattext, rankName, rankColor}

		-- Debug: Rank bilgilerini logla
		if rankName != "" then
			print("[Party Chat] " .. playerName .. " (" .. rankName .. "): " .. chattext)
		else
			print("[Party Chat] " .. playerName .. ": " .. chattext)
		end

		net.Start("PartyChat")
			net.WriteTable(chattable)
		net.Send(Chatmembers)
		hook.Run("SPSChat", ply, parties[ply:GetParty()], chattext)
	end
	
	hook.Add( "PlayerSay", "PartyChatMessage", function(ply,text)
		if string.Left( text, string.len(party.partychatcommand) + 1 ) == party.partychatcommand.." " then  
		ServerLog("[Party] "..ply:Nick() ..": ".. text.."\n")
			if ply:GetParty() != nil then			
				SendPartyMessage(ply, text)
			end
		return ""
		end
	end)

-- Server test komutu
concommand.Add("test_party_chat_server", function(ply, cmd, args)
	if not ply:IsAdmin() then return end

	if ply:GetParty() then
		print("✅ " .. ply:Nick() .. " partide")

		local partyID = ply:GetParty()
		if parties[partyID] and parties[partyID].memberRanks then
			local rankID = parties[partyID].memberRanks[ply:SteamID64()] or "member"
			if parties[partyID].ranks and parties[partyID].ranks[rankID] then
				local rankName = parties[partyID].ranks[rankID].name or ""
				print("Rütbe: " .. rankName)
			end
		end

		-- Test mesajı gönder
		SendPartyMessage(ply, "/p Test mesajı - rank sistemi çalışıyor!")
	else
		print("❌ " .. ply:Nick() .. " partide değil")
	end
end)

print("[Party System] Server parti chat sistemi yüklendi")
end




if CLIENT then
	net.Receive("PartyChat", function(len, CLIENT)
		local chattable = net.ReadTable()
		local playerName = chattable[1] or "Unknown"
		local message = chattable[2] or ""
		local rankName = chattable[3] or ""
		local rankColor = chattable[4] or Color(255, 255, 255)

		-- Rank bilgisiyle chat mesajını oluştur
		if rankName != "" and rankName != "Üye" then
			-- Lider için özel gösterim
			if rankName == "Lider" then
				chat.AddText(
					party.partychatcolr, party.language["[Party]"],
					Color(255, 255, 255), " ",
					Color(255, 215, 0), "★ [" .. rankName .. "] ",
					rankColor, playerName .. ": ",
					party.partychatmsgcolr, message
				)
			else
				-- Normal rank: [Party] [Rank] PlayerName: Message
				chat.AddText(
					party.partychatcolr, party.language["[Party]"],
					Color(255, 255, 255), " ",
					rankColor, "[" .. rankName .. "] ",
					rankColor, playerName .. ": ",
					party.partychatmsgcolr, message
				)
			end
		else
			-- Rank yoksa normal: [Party] PlayerName: Message
			chat.AddText(
				party.partychatcolr, party.language["[Party]"],
				Color(255, 255, 255), " ",
				party.partychatnamecolr, playerName .. ": ",
				party.partychatmsgcolr, message
			)
		end
	end)

	-- Test komutu
	concommand.Add("test_party_chat", function()
		if LocalPlayer():GetParty() then
			print("✅ Partidesiniz! /p komutuyla test edin")
			print("Örnek: /p Merhaba parti!")

			local partyID = LocalPlayer():GetParty()
			if parties[partyID] and parties[partyID].memberRanks then
				local rankID = parties[partyID].memberRanks[LocalPlayer():SteamID64()] or "member"
				if parties[partyID].ranks and parties[partyID].ranks[rankID] then
					local rankName = parties[partyID].ranks[rankID].name or ""
					print("Sizin rütbeniz: " .. rankName)
				end
			end
		else
			print("❌ Parti bulunamadı! Önce parti oluşturun.")
		end
	end)

	print("[Party System] Parti chat sistemi yüklendi:")
	print("  - /p <mesaj>: Parti sohbeti")
	print("  - test_party_chat: Chat sistemini test et")
end