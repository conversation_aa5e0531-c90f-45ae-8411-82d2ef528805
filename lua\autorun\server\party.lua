parties = {}
-- DevNote 225
if party.DarkrpGamemode then
	timer.Simple(1, function() --Makes sure the table is loaded before trying to use it
		for k, v in pairs (party.AutoGroupedJobs) do
			parties[k] ={}  
			parties[k].members = {}
			if party.AutoGroupedJobs[k].Name then
				parties[k].name = party.AutoGroupedJobs[k].Name
			else
				parties[k].name = team.GetName(party.AutoGroupedJobs[k]["Jobs"][1])
			end
			parties[k].Autogrouped = true
		end
	end)
end
local meta = FindMetaTable("Player")
  
util.AddNetworkString( "party")
util.AddNetworkString( "partiesmenu")
util.AddNetworkString( "joinrequest")
util.AddNetworkString( "partyinvite")
util.AddNetworkString( "onepartytoparty")
util.AddNetworkString( "oneparty")
util.AddNetworkString( "CreateRank")
util.AddNetworkString( "EditRank")
util.AddNetworkString( "DeleteRank")
util.AddNetworkString( "PromotePlayer")
util.AddNetworkString( "DemotePlayer")
util.AddNetworkString( "TransferLeadership")

function sendpartiestocl(ply)
	local ply = ply
	if istable(ply) then
		local plytab = {}
		for v,k in pairs(ply)do
			table.insert(plytab, player.GetBySteamID64(k))
		end
		ply = plytab
	end
	net.Start("party")
	net.WriteTable(parties)
	net.Send(ply)
end


function sendonepartiestocl(party, ply)
	net.Start("oneparty")
		net.WriteString(party)
		if parties[party] then
			--PrintTable(parties[party])
			net.WriteTable(parties[party])
		else
			net.WriteTable({name = "DeleteMe"}) 
		end
		
		net.Send(ply)
	
end

function SendOnePartyToEveryone(party)
	for v,k in ipairs(player.GetAll()) do
		sendonepartiestocl(party , k)
	end
end

function sendonepartiestoparty(party)
	SendOnePartyToEveryone(party)
	if parties[party] != nil then
		local plytab = {}
		for v,k in pairs(parties[party].members)do
			table.insert(plytab, player.GetBySteamID64(k))
		end
		net.Start("onepartytoparty")
		net.WriteString(party)
		net.WriteTable(parties[party])
		net.Send(plytab)					
	end
end



function openpartymenu( ply, text )
	if (text == party.chatcommand )then
		net.Start("partiesmenu", ply)
		net.Send(ply)
		sendpartiestocl(ply)
		return ""
	end
end
	hook.Add( "PlayerSay", "openpartymenu", openpartymenu )

	
function meta:Startparty(name)
	local CanJoin = hook.Call( "CanJoinParty" ,_ , self, self:SteamID64()  )
	if CanJoin != false then
		self:LeaveParty()
		parties[self:SteamID64()] = {}
		parties[self:SteamID64()].members = {self:SteamID64()}
		if name != "" then
			parties[self:SteamID64()].name = string.Left( name , 20 )
		else
			parties[self:SteamID64()].name = self:Nick()
		end
		
		-- Rank sistemi ekleme
		parties[self:SteamID64()].ranks = table.Copy(party.defaultRanks)
		parties[self:SteamID64()].memberRanks = {}
		parties[self:SteamID64()].memberRanks[self:SteamID64()] = "leader"
		
		sendonepartiestoparty(self:SteamID64())
		self.invitedcheck = {}
		self.invited = {}
		hook.Run("SPSStartParty", self, parties[self:SteamID64()])
	end
end
	
-- concommand.Add( "Startparty", function(ply,_e,args)
	-- ply:Startparty(args[1])
-- end )


util.AddNetworkString( "StartParty" )

net.Receive( "StartParty", function( len, ply )
	 	if ply.NextRequest == nil then 
			ply.NextRequest = 0
		end
		
		if ( ply.NextRequest < CurTime() ) then
			ply.NextRequest = CurTime() + party.joinrequestcooldown
			local name = net.ReadString()	
			ply:Startparty(name)
		end
end )



function meta:joinparty(jointheparty)
	local CanJoin = hook.Call( "CanJoinParty" ,_ , self, jointheparty  )
	if CanJoin then
		self:LeaveParty()
		for v, k in pairs(parties) do
			if v != self:SteamID64() then
				if self:GetParty() == v then
					self:LeaveParty()
				end
			end
		end
		if !table.HasValue(jointheparty.members , self:SteamID64()) then
			if (table.Count(jointheparty.members) < party.maxplayers) or jointheparty.Autogrouped then
				table.insert(jointheparty.members, self:SteamID64())
				
				-- Yeni üyeye default rank atama
				local partyID = nil
				for id, p in pairs(parties) do
					if p == jointheparty then
						partyID = id
						break
					end
				end
				
				if partyID and parties[partyID].memberRanks then
					parties[partyID].memberRanks[self:SteamID64()] = "member"
				end
				
				hook.Run("SPSJoinParty", self, jointheparty)
				sendonepartiestoparty(self:GetParty())
			else
				self:ChatPrint( party.language["Maximum number of players in this party."].." (" .. party.maxplayers .. ")" )	
			end
		end

	else
		self:ChatPrint( party.language["You are not allowed to join this party."])
	end
	
end

function meta:requestjoin(steam64)
	local CanJoin = hook.Call( "CanJoinParty" ,_ , self, steam64  )
	if CanJoin != false then
		self.requestedtojoin = steam64
		if self.NextRequest == nil then 
			self.NextRequest = 0
		end
		if ( self.NextRequest < CurTime() ) then
			self.NextRequest = CurTime() + party.joinrequestcooldown
			for v, k in pairs(parties) do
				if v == steam64 then
					net.Start("joinrequest")
					net.WriteString(self:SteamID64() )
					net.Send(player.GetBySteamID64(steam64))
					hook.Run("SPSRequestJoin", self, parties[steam64])
				end
			end
		else 
			self:ChatPrint( party.language["Please wait"].." " ..party.joinrequestcooldown.. " "..party.language["seconds between party requests."] )
		end
	else
		self:ChatPrint( party.language["You are not allowed to join this party."])
	end
end

-- concommand.Add( "requestjoin", function(ply,_e,args)
	-- ply:requestjoin(args[1])
-- end)

util.AddNetworkString( "RequestJoin" )

net.Receive( "RequestJoin", function( len, ply )
	 local id = net.ReadString()
		ply:requestjoin(id)
end )

function meta:answerjoinrequest(steamid64joiner, bool)
	if not IsValid(player.GetBySteamID64(steamid64joiner)) then return end 
	if bool == true then
		if player.GetBySteamID64(steamid64joiner).requestedtojoin then 
		if player.GetBySteamID64(steamid64joiner).requestedtojoin == self:SteamID64() then
			player.GetBySteamID64(steamid64joiner):joinparty(parties[self:SteamID64()])
			player.GetBySteamID64(steamid64joiner).requestedtojoin = nil
			player.GetBySteamID64(steamid64joiner):ChatPrint( self:Nick().. ": "..party.language["accepted your party request."] )
			hook.Run("SPSRequestResponse", self,parties[self:SteamID64()], player.GetBySteamID64(steamid64joiner), true)
		end
		end
	elseif bool == false then
		player.GetBySteamID64(steamid64joiner).requestedtojoin = nil
		player.GetBySteamID64(steamid64joiner):ChatPrint( self:Nick().. ": "..party.language["declined your party request."] )
		hook.Run("SPSRequestResponse", self, parties[self:SteamID64()], player.GetBySteamID64(steamid64joiner), false)
	end
end

-- concommand.Add( "answerjoinrequest", function(ply,_e,args)
	-- ply:answerjoinrequest(args[1], args[2] )
-- end)


util.AddNetworkString( "RequestedJoin" )
	 local delay1 = party.invitecooldown
	 local lastOccurance1 = -delay1
	 
net.Receive( "RequestedJoin", function( len, ply )
	local timeElapsed = CurTime() - lastOccurance1
	if timeElapsed < delay1 then
		ply:ChatPrint( "You are sending join requests too fast" )
	else
		local id = net.ReadString()
		local tfbool = net.ReadBool()
		ply:answerjoinrequest(id, tfbool)
		lastOccurance1 = CurTime()
	end
end )

function meta:answerinvite(steamid64inviter, bool)
	local CanJoin = hook.Call( "CanJoinParty" ,_ , player.GetBySteamID64(self:SteamID64()), steamid64inviter  )
	if player.GetBySteamID64(steamid64inviter) != false then
		if player.GetBySteamID64(steamid64inviter).invitedcheck then
			if CanJoin then
				if bool == true then
					if table.HasValue(player.GetBySteamID64(steamid64inviter).invitedcheck, self:SteamID64()) then
						self:joinparty(parties[steamid64inviter])
						player.GetBySteamID64(steamid64inviter):ChatPrint( self:Nick().. ": "..party.language["accepted your party invite."] )
						table.RemoveByValue(player.GetBySteamID64(steamid64inviter).invitedcheck, self:SteamID64() )
					end
				end
			else
				if table.HasValue(player.GetBySteamID64(steamid64inviter).invitedcheck, self:SteamID64()) then
					player.GetBySteamID64(steamid64inviter).requestedtojoin = nil
					player.GetBySteamID64(steamid64inviter):ChatPrint( self:Nick().. ": "..party.language["declined your party invite."] )
					table.RemoveByValue(player.GetBySteamID64(steamid64inviter).invitedcheck, self:SteamID64() )
				end
			end
			if bool == false then
				if table.HasValue(player.GetBySteamID64(steamid64inviter).invitedcheck, self:SteamID64()) then
					player.GetBySteamID64(steamid64inviter).requestedtojoin = nil
					player.GetBySteamID64(steamid64inviter):ChatPrint( self:Nick().. ": "..party.language["declined your party invite."] )
					table.RemoveByValue(player.GetBySteamID64(steamid64inviter).invitedcheck, self:SteamID64() )
				end
			end
		else
			self:ChatPrint( "This party is no longer valid" )
		end
	end
	if party.AutoGroupedJobs[tonumber(steamid64inviter)] != nil then
		if CanJoin then
			if bool == true then
				self:joinparty(parties[tonumber(steamid64inviter)])	
			end
			if bool == false then	 
				table.RemoveByValue(player.GetBySteamID64(steamid64inviter).invitedcheck, self:SteamID64() )
			end
		else
			table.RemoveByValue(player.GetBySteamID64(steamid64inviter).invitedcheck, self:SteamID64() )
		end
	end
end 

-- concommand.Add( "answerinvite", function(ply,_e,args)
	-- ply:answerinvite(args[1], args[2] )
-- end)


util.AddNetworkString( "AnswerInvite" )

net.Receive( "AnswerInvite", function( len, ply )
	 local id = net.ReadString()
	 local tfbool = net.ReadBool()
		ply:answerinvite(id, tfbool)
end )


function meta:partyinvite(steamid)
	-- Rank sistemi ile invite yetkisi kontrolü
	if not self:HasPartyPermission("invite") then
		self:ChatPrint(party.language["You don't have permission"])
		return
	end

	local CanJoin = hook.Call( "CanJoinParty" ,_ , player.GetBySteamID64(steamid), self:SteamID64()  )
	if CanJoin != false then
		if self.invited[steamid] == nil then
			self.invited[steamid] = {}
		end
		if self.invited[steamid].curtime == nil then
			self.invited[steamid].curtime = 0
		end
		if ( self.invited[steamid].curtime < CurTime() ) then
			self.invited[steamid].curtime = CurTime() + party.invitecooldown
				net.Start("partyinvite")
				net.WriteString(self:SteamID64())
				net.Send(player.GetBySteamID64(steamid))
				hook.Run("SPSPartyInvite", self, parties[self:SteamID64()], player.GetBySteamID64(steamid) )
		else
			self:ChatPrint( party.language["Please wait"].." "..party.invitecooldown.." "..party.language["seconds between party invites."] )
		end
	end
end

-- concommand.Add( "partyinvite", function(ply,_e,args)
	-- ply:partyinvite(args[1])
	-- table.insert(ply.invitedcheck,args[1])
-- end)

util.AddNetworkString( "PartyInvite" )
	 local delay = party.invitecooldown
	 local lastOccurance = -delay
	 
net.Receive( "PartyInvite", function( len, ply )    ----HERE
	local timeElapsed = CurTime() - lastOccurance
		if timeElapsed < delay then
			ply:ChatPrint( "You are sending invites too fast" )
		else
			local timeElapsed = CurTime() - lastOccurance
			local id = net.ReadString()
			ply:partyinvite(id)
			table.insert(ply.invitedcheck,id)
			lastOccurance = CurTime()
		end
end )




function meta:LeaveParty() 
	local CanLeave = hook.Run("CanLeaveParty", self, self:GetParty())
	if CanLeave != false then
		for v, k in pairs(parties) do
			if v == self:SteamID64() then
				self:disbandparty(self:SteamID64())
				--parties[self:SteamID64()] = nil
				self.invitedcheck = nil
			else
				if table.HasValue(parties[v].members, self:SteamID64()) then
					table.RemoveByValue(parties[v].members, self:SteamID64() )
					hook.Run("SPSLeaveParty",self, parties[v])
					sendonepartiestocl(v, self)
					sendonepartiestoparty(v)
				end
			end
		end
	end
	
end

function meta:kickfromparty(steam64)
	for v, k in pairs(parties) do
		if table.HasValue(parties[v].members, steam64) then
			-- Liderin kendini kicklememesi için kontrol
			if steam64 == v then
				self:ChatPrint("Lider kendini partiden atamaz! Parti dağıtmak için 'Disband Party' kullanın.")
				return
			end

			-- Rank sistemi ile kick yetkisi kontrolü
			local hasPermission = false
			if v == self:SteamID64() or self:IsAdmin() or table.HasValue(party.Admins, self:GetNWString("usergroup")) or table.HasValue(party.SteamIDAdmins, self:SteamID64()) then
				hasPermission = true
			elseif self:HasPartyPermission("kick") then
				hasPermission = true
			end

			if hasPermission then
				if player.GetBySteamID64(steam64) != false then
					hook.Run("SPSKickedParty", self, player.GetBySteamID64(steam64), parties[player.GetBySteamID64(steam64):GetParty()])
					player.GetBySteamID64(steam64):LeaveParty()
					player.GetBySteamID64(steam64):ChatPrint( self:Nick().. ": "..party.language["kicked you from the party."] )

				else
					for v, k in pairs(parties) do
						if table.HasValue(parties[v].members, steam64) then
							hook.Run("SPSKickedParty", self, player.GetBySteamID64(steam64), parties[player.GetBySteamID64(steam64):GetParty()])
							table.RemoveByValue(parties[v].members, steam64 )
							if parties[v].memberRanks then
								parties[v].memberRanks[steam64] = nil
							end
							sendonepartiestocl(v, player.GetBySteamID64(steam64))
							sendonepartiestoparty(v)
							player.GetBySteamID64(steam64):ChatPrint( self:Nick().. ": "..party.language["kicked you from the party."] )
						end
					end
				end
			else
				self:ChatPrint(party.language["You don't have permission"])
			end
		end
	end
end

-- concommand.Add( "kickfromparty", function(ply,_e,args)
	-- ply:kickfromparty(args[1]) 
-- end)

util.AddNetworkString( "KickFromParty" )

net.Receive( "KickFromParty", function( len, ply )
	local id = net.ReadString()
	ply:kickfromparty(id)
end )


function meta:disbandparty(steam64)
	for v, k in pairs(parties) do
		if parties[steam64] then
			if self:IsAdmin() or (self == player.GetBySteamID64(steam64)) or table.HasValue(party.Admins, self:GetNWString("usergroup")) or table.HasValue(party.SteamIDAdmins, self:SteamID64()) then
				hook.Run("SPSDisbandedParty", self, parties[v])
				local members = parties[steam64].members
				--PrintTable( members)
				--PrintTable(parties[steam64])
				parties[steam64] = nil
				sendonepartiestoparty(steam64)
				sendpartiestocl(members)
				if player.GetBySteamID64(steam64) != false then
					player.GetBySteamID64(steam64):ChatPrint( self:Nick().. ": "..party.language["disbanded your party."] )
				end
			end
		end
	end
end

-- concommand.Add( "disbandparty", function(ply,_e,args)
	-- ply:disbandparty(args[1])
-- end)

util.AddNetworkString( "DisbandParty" )

net.Receive( "DisbandParty", function( len, ply )
	local id = net.ReadString()
	ply:disbandparty(id)
end )


-- concommand.Add( "leaveparty", function(ply)
	-- ply:LeaveParty()
-- end)

util.AddNetworkString( "LeaveParty" )

net.Receive( "LeaveParty", function( len, ply )
	ply:LeaveParty()
end )


-- Rank yönetimi fonksiyonları
function meta:CreatePartyRank(rankID, rankName, rankColor, permissions)
	if not self:HasPartyPermission("createRank") then
		self:ChatPrint(party.language["You don't have permission"])
		return
	end

	local partyID = self:GetParty()
	if not partyID or not parties[partyID] then return end

	-- Rank ID'nin benzersiz olduğunu kontrol et
	if parties[partyID].ranks[rankID] then
		self:ChatPrint("Bu rank ID zaten mevcut!")
		return
	end

	parties[partyID].ranks[rankID] = {
		name = rankName,
		color = rankColor,
		permissions = permissions
	}

	sendonepartiestoparty(partyID)
end

function meta:EditPartyRank(rankID, rankName, rankColor, permissions)
	if not self:HasPartyPermission("createRank") then
		self:ChatPrint(party.language["You don't have permission"])
		return
	end

	local partyID = self:GetParty()
	if not partyID or not parties[partyID] or not parties[partyID].ranks[rankID] then return end

	-- Leader rankını düzenlemesini engelle
	if rankID == "leader" then
		self:ChatPrint("Lider rütbesi düzenlenemez!")
		return
	end

	parties[partyID].ranks[rankID] = {
		name = rankName,
		color = rankColor,
		permissions = permissions
	}

	sendonepartiestoparty(partyID)
end

function meta:DeletePartyRank(rankID)
	if not self:HasPartyPermission("createRank") then
		self:ChatPrint(party.language["You don't have permission"])
		return
	end

	local partyID = self:GetParty()
	if not partyID or not parties[partyID] or not parties[partyID].ranks[rankID] then return end

	-- Leader ve default rankları silinmesini engelle
	if rankID == "leader" or rankID == "member" or rankID == "officer" then
		self:ChatPrint("Bu rütbe silinemez!")
		return
	end

	-- Bu rütbeye sahip üyeleri member yap
	for memberID, memberRank in pairs(parties[partyID].memberRanks) do
		if memberRank == rankID then
			parties[partyID].memberRanks[memberID] = "member"
		end
	end

	parties[partyID].ranks[rankID] = nil
	sendonepartiestoparty(partyID)
end

function meta:PromotePlayer(targetSteamID, newRankID)
	if not self:HasPartyPermission("promote") then
		self:ChatPrint(party.language["You don't have permission"])
		return
	end

	local partyID = self:GetParty()
	if not partyID or not parties[partyID] then return end

	-- Hedef oyuncunun partide olduğunu kontrol et
	if not table.HasValue(parties[partyID].members, targetSteamID) then
		self:ChatPrint("Bu oyuncu partide değil!")
		return
	end

	-- Liderin kendi rütbesini değiştirmesini engelle
	if targetSteamID == partyID then
		self:ChatPrint("Lider kendi rütbesini değiştiremez!")
		return
	end

	-- Yeni rütbenin var olduğunu kontrol et
	if not parties[partyID].ranks[newRankID] then
		self:ChatPrint("Bu rütbe mevcut değil!")
		return
	end

	-- Leader rütbesine terfi etmeyi engelle (sadece transfer leadership ile)
	if newRankID == "leader" then
		self:ChatPrint("Liderlik sadece devretme ile verilebilir!")
		return
	end

	parties[partyID].memberRanks[targetSteamID] = newRankID

	local targetPlayer = player.GetBySteamID64(targetSteamID)
	if targetPlayer then
		targetPlayer:ChatPrint(self:Nick() .. " sizi " .. parties[partyID].ranks[newRankID].name .. " rütbesine terfi ettirdi!")
	end

	sendonepartiestoparty(partyID)
end

function meta:TransferLeadership(targetSteamID)
	local partyID = self:GetParty()
	if not partyID or not parties[partyID] then return end

	-- Sadece lider devredebilir
	if self:SteamID64() != partyID then
		self:ChatPrint("Sadece parti lideri liderliği devredebilir!")
		return
	end

	-- Hedef oyuncunun partide olduğunu kontrol et
	if not table.HasValue(parties[partyID].members, targetSteamID) then
		self:ChatPrint("Bu oyuncu partide değil!")
		return
	end

	-- Parti verilerini kopyala
	local oldPartyData = table.Copy(parties[partyID])

	-- Eski lideri normal üye yap
	oldPartyData.memberRanks[self:SteamID64()] = "member"

	-- Yeni lideri leader yap
	oldPartyData.memberRanks[targetSteamID] = "leader"

	-- Yeni parti oluştur
	parties[targetSteamID] = oldPartyData
	parties[partyID] = nil

	-- Herkese bildir
	sendonepartiestoparty(targetSteamID)

	local targetPlayer = player.GetBySteamID64(targetSteamID)
	if targetPlayer then
		targetPlayer:ChatPrint(party.language["Leadership transferred"] .. " " .. self:Nick() .. " tarafından!")
		self:ChatPrint("Liderlik " .. targetPlayer:Nick() .. " oyuncusuna devredildi!")
	end

	hook.Run("SPSPartyLeadershipTransferred", self, targetPlayer, parties[targetSteamID])
end

function partyleaderleft( ply )
	for v, k in pairs(parties) do
		if v == ply:SteamID64() then
			-- Liderlik devretme yetkisi olan birini bul
			local newLeader = nil
			for _, memberID in pairs(parties[v].members) do
				if memberID != ply:SteamID64() and player.GetBySteamID64(memberID) and
				   parties[v].memberRanks[memberID] and
				   parties[v].ranks[parties[v].memberRanks[memberID]] and
				   parties[v].ranks[parties[v].memberRanks[memberID]].permissions["transferLeadership"] then
					newLeader = memberID
					break
				end
			end

			if newLeader then
				-- Parti verilerini kopyala
				local oldPartyData = table.Copy(parties[v])

				-- Yeni liderin rütbesini leader yap
				oldPartyData.memberRanks[newLeader] = "leader"

				-- Eski lideri çıkar
				table.RemoveByValue(oldPartyData.members, ply:SteamID64())
				oldPartyData.memberRanks[ply:SteamID64()] = nil

				-- Yeni parti oluştur
				parties[newLeader] = oldPartyData
				parties[v] = nil

				-- Herkese bildir
				sendonepartiestoparty(newLeader)
				hook.Run("SPSPartyLeadershipTransferred", ply, player.GetBySteamID64(newLeader), parties[newLeader])
			else
				-- Liderlik devretme yetkisi olan kimse yoksa normal dağıt
				hook.Run("SPSPartyLeaderLeft", ply, parties[v])
				local members = parties[v].members
				parties[v] = nil
				ply.invitedcheck = nil
				sendonepartiestoparty(v)
				sendpartiestocl(members)
			end
		else
			if party.kickondisconnect then
				if table.HasValue(parties[v].members, ply:SteamID64()) then
					table.RemoveByValue(parties[v].members, ply:SteamID64())
					if parties[v].memberRanks then
						parties[v].memberRanks[ply:SteamID64()] = nil
					end
					sendonepartiestoparty(v)
				end
			end
		end
	end
end
hook.Add( "PlayerDisconnected", "partyleaderleft", partyleaderleft )


function partydamage(victim, attacker )
	if party.PartyDamage != true then
		if victim:IsPlayer() and attacker:IsPlayer() then
			if (victim:GetParty() != nil) and (attacker:GetParty() != nil ) then
				if (victim:GetParty() == attacker:GetParty()) then
					if victim != attacker then
						return false
					end
				end
			end
		end
	end
end
hook.Add( "PlayerShouldTakeDamage", "partydamage", partydamage)

if party.DarkrpGamemode then
	function Party_TeamChange(ply, before, after)
	local GroupedJobJoin
		for v,k in pairs (party.AutoGroupedJobs) do
			if table.HasValue(party.AutoGroupedJobs[v]["Jobs"], after) then
				GroupedJobJoin = v
			end
		end
		for v,k in pairs (party.AutoGroupedJobs) do
			if table.HasValue(party.AutoGroupedJobs[v]["Jobs"], before) then
				if ply:GetParty() == v then
					ply:LeaveParty()
				end
			end
		end

		
		if GroupedJobJoin != nil then
			if party.ForceJobParty == true then
				ply:LeaveParty()
				ply:joinparty(parties[GroupedJobJoin])
			else
				TeamPartyInvite(ply:SteamID64(), GroupedJobJoin)
			end
		end
		if party.KickBlacklistJobs == true then
			if table.HasValue(party.BlacklistJobs, after) then
				ply:ChatPrint( party.language["You joined a job that is not allowed to be in a party. Kicking you from party"])
				ply:LeaveParty()
			end
		end
		
	end
	hook.Add("OnPlayerChangedTeam", "Party_PlayerChangedTeams", Party_TeamChange)
end

function TeamPartyInvite(steamid, team)
	local CanJoin = hook.Call( "CanJoinParty" ,_ , player.GetBySteamID64(steamid), team  )
	if CanJoin != false then
		net.Start("partyinvite")
		net.WriteString(team)
		net.Send(player.GetBySteamID64(steamid))
	end
end

function GroupedCanJoin(ply, tojoinparty)
	local canjoin = true
	if table.HasValue(party.BlacklistJobs, ply:Team()) then
		print(ply:Team())
		canjoin = false
	end
	
	if party.ForceJobParty == true then
		if party.AutoGroupedJobs[tojoinparty] then
			if !table.HasValue(party.AutoGroupedJobs[tojoinparty]["Jobs"], ply:Team()) then
				canjoin = false
			end
		end
		for v,k in pairs(party.AutoGroupedJobs)do
			if ply:GetParty() == v and tojoinparty != parties[ply:GetParty()]then
				ply:ChatPrint( party.language["You are currently in a forced party, change jobs."])
				canjoin = false
			end
		end
	end
	
	return canjoin
end
hook.Add("CanJoinParty", "GroupedCanJoin" , GroupedCanJoin )


function GroupedCanLeave(ply, toleaveparty)
if party.ForceJobParty == true then
	for v,k in pairs(party.AutoGroupedJobs)do
			if party.AutoGroupedJobs[toleaveparty] then
				if table.HasValue(party.AutoGroupedJobs[toleaveparty]["Jobs"], ply:Team()) then
					return false
				end
			end
		end
	end
end
hook.Add("CanLeaveParty", "GroupedCanLeave" , GroupedCanLeave )




-- Network receivers for rank management
net.Receive("CreateRank", function(len, ply)
	local rankID = net.ReadString()
	local rankName = net.ReadString()
	local rankColor = net.ReadColor()
	local permissions = net.ReadTable()

	ply:CreatePartyRank(rankID, rankName, rankColor, permissions)
end)

net.Receive("EditRank", function(len, ply)
	local rankID = net.ReadString()
	local rankName = net.ReadString()
	local rankColor = net.ReadColor()
	local permissions = net.ReadTable()

	ply:EditPartyRank(rankID, rankName, rankColor, permissions)
end)

net.Receive("DeleteRank", function(len, ply)
	local rankID = net.ReadString()
	ply:DeletePartyRank(rankID)
end)

net.Receive("PromotePlayer", function(len, ply)
	local targetSteamID = net.ReadString()
	local newRankID = net.ReadString()

	ply:PromotePlayer(targetSteamID, newRankID)
end)

net.Receive("TransferLeadership", function(len, ply)
	local targetSteamID = net.ReadString()
	ply:TransferLeadership(targetSteamID)
end)

local function partyspawn( ply )
	sendpartiestocl(ply)
end
hook.Add( "PlayerInitialSpawn", "partyspawn", partyspawn )

-- Server Test Komutları
concommand.Add("create_colorful_ranks", function(ply, cmd, args)
	if not ply:IsAdmin() then return end

	local partyID = ply:GetParty()
	if not partyID then
		ply:Startparty("Renkli Parti")
		partyID = ply:GetParty()
	end

	-- Renkli test rütbeleri oluştur
	local testRanks = {
		{id = "vip", name = "VIP", color = Color(255, 215, 0), permissions = {invite = true}},
		{id = "moderator", name = "Moderatör", color = Color(100, 150, 255), permissions = {kick = true, invite = true}},
		{id = "trusted", name = "Güvenilir", color = Color(100, 255, 100), permissions = {invite = true}},
		{id = "newbie", name = "Yeni", color = Color(255, 150, 150), permissions = {}},
	}

	for _, rank in ipairs(testRanks) do
		ply:CreatePartyRank(rank.id, rank.name, rank.color, rank.permissions)
		print("✅ " .. rank.name .. " rütbesi oluşturuldu")
	end

	print("🎨 Renkli test rütbeleri oluşturuldu!")
	print("HUD'da farklı renklerde görünecekler.")
end)

concommand.Add("assign_test_ranks", function(ply, cmd, args)
	if not ply:IsAdmin() then return end

	local partyID = ply:GetParty()
	if not partyID then
		print("❌ Parti bulunamadı!")
		return
	end

	local availableRanks = {"vip", "moderator", "trusted", "newbie", "officer", "member"}
	local rankIndex = 1

	-- Parti üyelerine farklı rütbeler ata
	for _, memberID in pairs(parties[partyID].members) do
		if memberID != ply:SteamID64() then -- Lideri atla
			local rankID = availableRanks[rankIndex] or "member"
			ply:PromotePlayer(memberID, rankID)

			local member = player.GetBySteamID64(memberID)
			local memberName = member and member:Nick() or "Offline"
			print("✅ " .. memberName .. " -> " .. rankID)

			rankIndex = rankIndex + 1
			if rankIndex > #availableRanks then
				rankIndex = 1
			end
		end
	end

	print("🎯 Test rütbeleri atandı! HUD'da farklı renkler göreceksiniz.")
end)

concommand.Add("give_party_leader", function(ply, cmd, args)
	if not ply:IsAdmin() then return end

	local targetName = args[1]
	if not targetName then
		print("Kullanım: give_party_leader <oyuncu_adı>")
		return
	end

	local targetPlayer = nil
	for _, p in pairs(player.GetAll()) do
		if string.lower(p:Nick()) == string.lower(targetName) then
			targetPlayer = p
			break
		end
	end

	if not targetPlayer then
		print("Oyuncu bulunamadı: " .. targetName)
		return
	end

	-- Oyuncuyu parti lideri yap
	if not targetPlayer:GetParty() then
		targetPlayer:Startparty("Test Partisi")
	end

	print("Oyuncuya parti lideri yetkisi verildi: " .. targetPlayer:Nick())
end)

print("[Party System] Server test komutları yüklendi:")
print("  - create_colorful_ranks: Renkli test rütbeleri oluştur")
print("  - assign_test_ranks: Üyelere test rütbeleri ata")
print("  - give_party_leader <oyuncu>: Oyuncuya parti lideri yetkisi ver")
