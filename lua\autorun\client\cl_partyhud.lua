--GUI base created by billy
--https://scriptfodder.com/users/view/76561198040894045/scripts


local Party = {}
local disconnectedicon = "icon16/disconnect.png"
local leadericon = "icon16/award_star_gold_1.png"
local wantedicon = "icon16/exclamation.png"
CreateClientConVar("party.hudhorizontalpos", party.hudhorizontalpos, true)
CreateClientConVar("party.hudverticalpos", party.hudverticalpos, true)
party.hudverticalpos = GetConVar( "party.hudverticalpos" ):GetInt()
party.hudhorizontalpos = GetConVar( "party.hudhorizontalpos" ):GetInt()

surface.CreateFont("roboto16",{
	size = 16,
	font = "Roboto",
})
party.DisplayParty = party.DisplayParty or true
	local function safeText(text)
		return string.match(text, "^#([a-zA-Z_]+)$") and text .. " " or text
	end
if !draw.DrawNonParsedText then



	function draw.DrawNonParsedText(text, font, x, y, color, xAlign)
		return draw.DrawText(safeText(text), font, x, y, color, xAlign)
	end
end


if SERVER then return end

-- 📌 Font Tanımları
surface.CreateFont("comfortaa_big", {
    font = "Comfortaa",
    size = 18,
    weight = 500,
    extended = true,
    antialias = true
})

surface.CreateFont("comfortaa_small", {
    font = "Comfortaa",
    size = 15,
    weight = 500,
    extended = true,
    antialias = true
})

-- 📌 Renkleri Ön Belleğe Al
local color_background = Color(25, 25, 35, 220)
local color_panel = Color(35, 35, 50, 255)
local color_text = Color(220, 220, 220)
local color_health = Color(200, 50, 50, 255)
local color_armor = Color(50, 100, 200, 255)
local color_dead = Color(255, 50, 50)

-- 📌 Materyalleri Ön Belleğe Al
local mat_wanted = Material("icon16/star.png")  
local mat_disconnected = Material("icon16/disconnect.png")
local mat_leader = Material("icon16/award_star_gold_3.png") -- Lider simgesi

-- 📌 HUD Çizme
hook.Add("HUDPaint", "draw_party_hud", function()
    if not GetConVar("party_showhud"):GetBool() then return end

    local ply = LocalPlayer()
    if not IsValid(ply) or not parties or not ply:GetParty() then return end

    local partyID = ply:GetParty()
    local partyData = parties[partyID]
    if not partyData or not partyData.members then return end

    local members = partyData.members
    if table.IsEmpty(members) then return end  

    -- 📌 SOL ÜST KÖŞEYE SABİTLEYELİM
    local x = 20  -- Sabit bir X değeri (ekranın soluna sabitle)
    local y = 80  -- Üstten biraz boşluk bırak

    local panel_width, panel_height = 200, 50  -- Sabit panel boyutları
    local icon_size = 16  -- İkon boyutu

    -- 📌 Parti İsmi
    draw.SimpleText(party.language["Party Name"] .. ": " .. partyData.name, "comfortaa_big", x + 100, y + 38, color_text, TEXT_ALIGN_CENTER)

    for index, steamID in ipairs(members) do
        local member = player.GetBySteamID64(steamID)
        local pos_y = y + index * (panel_height + 5)  -- Her satırı düzenli aralıklarla çiz

        -- 📌 Panel Arka Planı
        draw.RoundedBox(8, x, pos_y, panel_width, panel_height, color_background)
        draw.RoundedBox(8, x + 3, pos_y + 3, panel_width - 6, panel_height - 6, color_panel)

        if IsValid(member) then
            local health = math.Clamp(100 * (1 / (member:GetMaxHealth() / member:Health())), 0, 100)
            local armor = math.Clamp(member:Armor(), 0, 100)

            -- 📌 İsim ve Rank
            local rankColor = Color(255, 255, 255) -- Default renk
            local rankName = ""

            -- Rank bilgilerini al
            if member.GetPartyRankColor and member.GetPartyRankName then
                rankColor = member:GetPartyRankColor() or Color(255, 255, 255)
                rankName = member:GetPartyRankName() or ""
            else
                -- Fallback: Parti verilerinden al
                local partyID = member:GetParty()
                if partyID and parties[partyID] and parties[partyID].memberRanks then
                    local rankID = parties[partyID].memberRanks[member:SteamID64()] or "member"
                    if parties[partyID].ranks and parties[partyID].ranks[rankID] then
                        rankColor = parties[partyID].ranks[rankID].color or Color(255, 255, 255)
                        rankName = parties[partyID].ranks[rankID].name or ""
                    end
                end
            end

            -- İsmi rank rengiyle göster
            draw.SimpleText(member:Nick(), "comfortaa_small", x + 10, pos_y + 5, rankColor, TEXT_ALIGN_LEFT)

            -- Rank ismini göster
            if rankName != "" and rankName != "Üye" then
                draw.SimpleText("[" .. rankName .. "]", "comfortaa_small", x + 10, pos_y + 18, rankColor, TEXT_ALIGN_LEFT)
                -- Job'ı sağa kaydır
                if member.getDarkRPVar then
                    draw.SimpleText(member:getDarkRPVar("job"), "comfortaa_small", x + 90, pos_y + 18, team.GetColor(member:Team()), TEXT_ALIGN_LEFT)
                end
            else
                -- Rank yoksa job'ı normal yerde göster
                if member.getDarkRPVar then
                    draw.SimpleText(member:getDarkRPVar("job"), "comfortaa_small", x + 10, pos_y + 18, team.GetColor(member:Team()), TEXT_ALIGN_LEFT)
                end
            end
            -- 📌 Sağlık & Zırh Çubukları
            draw.RoundedBox(4, x + 10, pos_y + 35, 1.8 * health, 6, color_health)
            draw.RoundedBox(4, x + 10, pos_y + 42, 1.8 * armor, armor > 0 and 3 or 0, color_armor)

            -- 📌 Durum Bilgisi
            local statusText = member:Alive() and (member:Health() .. " - " .. member:Armor()) or "Ölü"
            local statusColor = member:Alive() and color_text or color_dead
            draw.SimpleText(statusText, "comfortaa_small", x + 10, pos_y + 30, statusColor, TEXT_ALIGN_LEFT)

            -- 📌 Lider İkonu (Parti lideri ise göster)
            if steamID == ply:GetParty() then
                -- Lider ikonunu altın renkte göster
                surface.SetDrawColor(255, 215, 0, 255)
                surface.SetMaterial(mat_leader)
                surface.DrawTexturedRect(
                    x + panel_width - icon_size - 5, -- Sağdan 5 piksel içeride
                    pos_y + 5, -- Panelin üst kısmına hizalanmış
                    icon_size, icon_size
                )

                -- Lider yazısı ekle
                draw.SimpleText("LİDER", "comfortaa_small", x + panel_width - 35, pos_y + 25, Color(255, 215, 0), TEXT_ALIGN_LEFT)
            end

            -- 📌 Wanted İkonu (Eğer oyuncu aranıyorsa)
            if member:isWanted() then
                surface.SetDrawColor(255, 50, 50, 255)
                surface.SetMaterial(mat_wanted)
                surface.DrawTexturedRect(
                    x + panel_width - icon_size - 5,
                    pos_y + 25,
                    icon_size, icon_size
                )
            end
        else
            -- 📌 Offline Durumu
            local offlineName = "Offline Player"
            local rankColor = color_dead
            local rankName = ""

            -- Offline oyuncunun rank bilgisini al
            if partyData.memberRanks and partyData.memberRanks[steamID] then
                local rankID = partyData.memberRanks[steamID]
                if partyData.ranks and partyData.ranks[rankID] then
                    rankName = partyData.ranks[rankID].name or ""
                    rankColor = partyData.ranks[rankID].color or color_dead
                end
            end

            -- Offline oyuncu ismini göster
            draw.SimpleText(offlineName, "comfortaa_small", x + 10, pos_y + 5, rankColor, TEXT_ALIGN_LEFT)

            -- Rank varsa göster
            if rankName != "" and rankName != "Üye" then
                draw.SimpleText("[" .. rankName .. "]", "comfortaa_small", x + 10, pos_y + 18, rankColor, TEXT_ALIGN_LEFT)
                draw.SimpleText("Çevrimdışı", "comfortaa_small", x + 90, pos_y + 18, color_dead, TEXT_ALIGN_LEFT)
            else
                draw.SimpleText("Çevrimdışı", "comfortaa_small", x + 10, pos_y + 18, color_dead, TEXT_ALIGN_LEFT)
            end
        end


    end
end)





local function PartyOpenCon()
if ( IsValid( Party_Panel ) ) then Party_Panel:Remove() end
	if parties[LocalPlayer():GetParty()] then
		Party_Panel = vgui.Create("DFrame", g_ContextMenu)
		Party_Panel:SetPos(party.hudhorizontalpos, party.hudverticalpos)
		Party_Panel:SetSize( 175, #parties[LocalPlayer():GetParty()].members * 60 + 30)
		Party_Panel:ShowCloseButton(false)
		Party_Panel:SetTitle("")
		--↔↕
		--Party_Paneldp = vgui.Create( "DPanel", g_ContextMenu )
		Party_Panel:SetMouseInputEnabled(true)
		function Party_Panel.Paint()
			if parties[LocalPlayer():GetParty()] then
				draw.RoundedBox(5,0,0, 175, #parties[LocalPlayer():GetParty()].members * 60 + 30, Color( 25, 25, 25, 50 ) ) 
				draw.DrawText("↕", "roboto16", 165,0, Color( 255, 255, 255, 255 ), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER )
				draw.DrawText("↔", "roboto16", 165,0, Color( 255, 255, 255, 255 ), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER )
				local hudx,hudy = Party_Panel:GetPos()
				party.hudhorizontalpos , party.hudverticalpos = math.Clamp(hudx , 0,ScrW() - 175) , math.Clamp(hudy , 0, ScrH() - (#parties[LocalPlayer():GetParty()].members * 60 + 30))
			end
		end
		if parties[LocalPlayer():SteamID64()] then
			for v,k in pairs (parties[LocalPlayer():GetParty()].members) do
				local position = (v * 60 + 2 )
				Party_PanelButton = vgui.Create( "PartyButton", Party_Panel)
				Party_PanelButton:SetText(party.language["Kick"])
				Party_PanelButton:SetPos(97, position)
				Party_PanelButton:SetSize(50, 20)
				function Party_PanelButton:DoClick() -- Defines what should happen when the label is clicked
					self:Remove()
					net.Start( "KickFromParty" )
					net.WriteString(k)
					net.SendToServer()
				end
			end
		end
	end
end
hook.Add( "OnContextMenuOpen", "PartyOpenCon", PartyOpenCon )

local function PartyCloseCon()
	GetConVar( "party.hudhorizontalpos" ):SetInt( party.hudhorizontalpos)
	GetConVar( "party.hudverticalpos" ):SetInt(party.hudverticalpos)
end
hook.Add( "OnContextMenuClose", "PartyCloseCon", PartyCloseCon )



--surface.CreateFont( "party_hud_font1", {
	-- font = "seguisb",
	-- size = 18,
	-- weight = 800,
	-- antialias = true,
	-- underline = false,
	-- italic = false,
	-- strikeout = false,
	-- symbol = false,
	-- rotary = false,
	-- shadow = false,
	-- additive = false,
	-- outline = false,
-- })

-- hook.Add( "HUDPaint", "drawpartyhud", function()
	-- if GetConVar( "party_showhud" ):GetInt() != 1 then
		-- if parties != nil and parties[LocalPlayer():GetParty()] != nil and parties[LocalPlayer():GetParty()].members != nil then
			-- draw.DrawText(party.language["Party Name"] ..": " ..parties[LocalPlayer():GetParty()].name, "roboto16", party.hudhorizontalpos, party.hudverticalpos+10, Color( 255, 255, 255, 255 ), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER )
			-- for v,k in pairs (parties[LocalPlayer():GetParty()].members) do
				-- local position = v * 55 - 30 + v * 5 + party.hudverticalpos
				-- local member = player.GetBySteamID64(k)
				-- draw.RoundedBox(4,party.hudhorizontalpos,position,150,55, party.backgroundcolor ) 
				-- draw.RoundedBox(4,1+ party.hudhorizontalpos,position+1,150-2,55-2,Color(34,34,34,225))
				-- if player.GetBySteamID64(k) !=  false then
				-- local health =  math.Clamp(100*(1/(member:GetMaxHealth() / member:Health())),0,100)
				-- local armor = math.Clamp(  member:Armor(), 0, 100 )
				-- draw.RoundedBoxEx( 2, 4+ party.hudhorizontalpos, position + 45 , 2.13* health/1.5, 5, Color( 192, 57, 43, 255 ), true, true, true, true )
				-- draw.RoundedBoxEx( 2, 4+ party.hudhorizontalpos, position + 48 , 2.13* armor/1.5, 3, Color( 41, 128, 185, 255 ), true, true, true, true )
				-- draw.DrawText(string.Left(member:Nick(), 18), "roboto16", 5+ party.hudhorizontalpos, position+ 5, Color( 255, 255, 255, 255 ), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER )
				-- else 
				-- draw.DrawText(party.language["offline"] , "roboto16", 5+ party.hudhorizontalpos, position+ 5, Color( 255, 255, 255, 255 ), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER )
				-- end
				-- if player.GetBySteamID64(k) !=  false then
					-- if member:Alive() then
					-- draw.DrawText(member:Health().."/"..member:GetMaxHealth(), "roboto16", 5+ party.hudhorizontalpos, position+ 25, Color( 255, 255, 255, 255 ), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER )
					-- else
					-- draw.DrawText( party.language["Dead"] , "roboto16", 5+ party.hudhorizontalpos, position+ 25, Color( 255, 255, 255, 255 ), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER )
					-- end
					-- if (DarkRP) then
						-- if member:getDarkRPVar("job") != nil then
							-- if string.len(member:getDarkRPVar("job")) >= 15 then
								-- draw.DrawText( string.Left(member:getDarkRPVar("job"), 13 ).."..", "roboto16", 145+ party.hudhorizontalpos, position+ 25, team.GetColor( member:Team( ) ), TEXT_ALIGN_RIGHT, TEXT_ALIGN_CENTER )
							-- else
								-- draw.DrawText( string.Left(member:getDarkRPVar("job"), 16 ), "roboto16", 145+ party.hudhorizontalpos, position+ 25, team.GetColor( member:Team( ) ), TEXT_ALIGN_RIGHT, TEXT_ALIGN_CENTER )
							-- end
						-- end
						-- if member:isWanted() then
							-- surface.SetMaterial( Material( wantedicon ) )
							-- surface.SetDrawColor( 255, 255, 255, 255 )
							-- surface.DrawTexturedRect( 155+ party.hudhorizontalpos, position + 34, 16, 16 )
						-- else
							-- surface.SetMaterial( Material( wantedicon ) )
							-- surface.SetDrawColor( 255, 255, 255, party.fadediconsfadeamount )
							-- surface.DrawTexturedRect( 155+ party.hudhorizontalpos, position+ 34, 16, 16 )				
						-- end
					-- end
				-- end
				-- if player.GetBySteamID64(k) != false then
					-- surface.SetMaterial( Material( disconnectedicon ) )
					-- surface.SetDrawColor( 255, 255, 255, party.fadediconsfadeamount )
					-- surface.DrawTexturedRect( 155+ party.hudhorizontalpos, position + 16, 16, 16 )
				-- else
					-- surface.SetMaterial( Material( disconnectedicon ) )
					-- surface.SetDrawColor( 255, 255, 255, 255 )
					-- surface.DrawTexturedRect( 155+ party.hudhorizontalpos, position+ 16, 16, 16 )				
				-- end
				
				-- if k == LocalPlayer():GetParty() then
					-- surface.SetMaterial( Material( leadericon ) )
					-- surface.SetDrawColor( 255, 255, 255, 255 )
					-- surface.DrawTexturedRect( 155+ party.hudhorizontalpos, position + 0, 16, 16 )
				-- else
					-- surface.SetMaterial( Material( leadericon ) )
					-- surface.SetDrawColor( 255, 255, 255, party.fadediconsfadeamount )
					-- surface.DrawTexturedRect( 155+ party.hudhorizontalpos, position+ 0, 16, 16 )				
				-- end	
			-- end
		-- end
		
		-- local tr = LocalPlayer():GetEyeTrace()
		-- local ply = tr.Entity

		-- if ply:IsValid() and ply:IsPlayer() then
			-- if (ply:GetPos():DistToSqr(LocalPlayer():GetPos()) < 45000) and (parties != nil) and (ply:GetParty() != nil) then
				-- local partyhudname = ply:GetPartyName()
				-- draw.SimpleText("Party: " .. partyhudname, "party_hud_font1", ScrW()/2, ScrH() - 129, Color(0,0,0,255), TEXT_ALIGN_CENTER, TEXT_ALIGN_BOTTOM)
				-- draw.SimpleText("Party: " .. partyhudname, "party_hud_font1", ScrW()/2, ScrH() - 130, Color(255,255,255,255), TEXT_ALIGN_CENTER, TEXT_ALIGN_BOTTOM)
			-- end
		-- end
	-- end
-- end)

-- HUD Test Komutları
concommand.Add("test_hud_ranks", function()
	print("=== Parti HUD Rank Test ===")

	local ply = LocalPlayer()
	local partyID = ply:GetParty()

	if not partyID then
		print("❌ Parti bulunamadı! Önce parti oluşturun.")
		return
	end

	print("✅ Parti ID: " .. partyID)
	print("✅ Oyuncu Steam ID: " .. ply:SteamID64())
	print("✅ Lider mi: " .. tostring(ply:SteamID64() == partyID))

	-- Rank fonksiyonlarını test et
	if ply.GetPartyRankColor then
		local rankColor = ply:GetPartyRankColor()
		print("✅ Rank Rengi: " .. rankColor.r .. "," .. rankColor.g .. "," .. rankColor.b)
	else
		print("❌ GetPartyRankColor fonksiyonu bulunamadı!")
	end

	if ply.GetPartyRankName then
		local rankName = ply:GetPartyRankName()
		print("✅ Rank Adı: " .. rankName)
	else
		print("❌ GetPartyRankName fonksiyonu bulunamadı!")
	end

	-- Parti verilerini kontrol et
	if parties[partyID] then
		print("\n--- Parti Verileri ---")
		print("Parti Adı: " .. parties[partyID].name)

		if parties[partyID].memberRanks then
			print("Üye Rütbeleri:")
			for memberID, rankID in pairs(parties[partyID].memberRanks) do
				local member = player.GetBySteamID64(memberID)
				local memberName = member and member:Nick() or "Offline"
				print("  " .. memberName .. " (" .. memberID .. ") -> " .. rankID)
			end
		end

		if parties[partyID].ranks then
			print("Mevcut Rütbeler:")
			for rankID, rankData in pairs(parties[partyID].ranks) do
				local color = rankData.color
				print("  " .. rankID .. ": " .. rankData.name .. " (R:" .. color.r .. " G:" .. color.g .. " B:" .. color.b .. ")")
			end
		end
	else
		print("❌ Parti verileri bulunamadı!")
	end

	print("\n=== Test Tamamlandı ===")
end)

concommand.Add("toggle_party_hud", function()
	local currentValue = GetConVar("party_showhud"):GetInt()
	local newValue = currentValue == 1 and 0 or 1

	GetConVar("party_showhud"):SetInt(newValue)

	if newValue == 0 then
		print("✅ Parti HUD gizlendi")
	else
		print("✅ Parti HUD gösteriliyor")
	end
end)

print("[Party System] HUD test komutları yüklendi:")
print("  - test_hud_ranks: Rank bilgilerini test et")
print("  - toggle_party_hud: HUD'ı aç/kapat")
