--GUI base created by billy
--https://scriptfodder.com/users/view/76561198040894045/scripts


local Party = {}
local disconnectedicon = "icon16/disconnect.png"
local leadericon = "icon16/award_star_gold_1.png"
local wantedicon = "icon16/exclamation.png"
CreateClientConVar("party.hudhorizontalpos", party.hudhorizontalpos, true)
CreateClientConVar("party.hudverticalpos", party.hudverticalpos, true)
party.hudverticalpos = GetConVar( "party.hudverticalpos" ):GetInt()
party.hudhorizontalpos = GetConVar( "party.hudhorizontalpos" ):GetInt()

surface.CreateFont("roboto16",{
	size = 16,
	font = "Roboto",
})
party.DisplayParty = party.DisplayParty or true
	local function safeText(text)
		return string.match(text, "^#([a-zA-Z_]+)$") and text .. " " or text
	end
if !draw.DrawNonParsedText then



	function draw.DrawNonParsedText(text, font, x, y, color, xAlign)
		return draw.DrawText(safeText(text), font, x, y, color, xAlign)
	end
end


if SERVER then return end

-- 📌 Font Tanımları
surface.CreateFont("comfortaa_big", {
    font = "Comfortaa",
    size = 18,
    weight = 500,
    extended = true,
    antialias = true
})

surface.CreateFont("comfortaa_small", {
    font = "Comfortaa",
    size = 15,
    weight = 500,
    extended = true,
    antialias = true
})

surface.CreateFont("comfortaa_medium", {
    font = "Comfortaa",
    size = 18,
    weight = 600,
    extended = true,
    antialias = true
})

-- 📌 Renkleri Ön Belleğe Al
local color_background = Color(25, 25, 35, 220)
local color_panel = Color(35, 35, 50, 255)
local color_text = Color(220, 220, 220)
local color_health = Color(200, 50, 50, 255)
local color_armor = Color(50, 100, 200, 255)
local color_dead = Color(255, 50, 50)

-- 📌 Materyalleri Ön Belleğe Al
local mat_wanted = Material("icon16/star.png")  
local mat_disconnected = Material("icon16/disconnect.png")
local mat_leader = Material("icon16/award_star_gold_3.png") -- Lider simgesi

-- HUD scaling functions
local function GetScaleMultiplier()
    local scrW, scrH = ScrW(), ScrH()
    return math.min(scrW / 1920, scrH / 1080)
end

local function ScaleSize(size)
    return size * GetScaleMultiplier()
end

local function ScalePos(x, y)
    local scrW, scrH = ScrW(), ScrH()
    return x * scrW / 1920, y * scrH / 1080
end

-- 📌 HUD Çizme
hook.Add("HUDPaint", "draw_party_hud", function()
    if not GetConVar("party_showhud"):GetBool() then return end

    local ply = LocalPlayer()
    if not IsValid(ply) or not parties or not ply:GetParty() then return end

    local partyID = ply:GetParty()
    local partyData = parties[partyID]
    if not partyData or not partyData.members then return end

    local members = partyData.members
    if table.IsEmpty(members) then return end  

    -- 📌 Daha kompakt boyutlar (sabit değerler)
    local base_x, base_y = 20, 80
    local panel_width = 220  -- Daha dar panel
    local panel_height = 45  -- Daha kısa panel
    local icon_size = 14     -- Daha küçük ikonlar
    local margin = 4         -- Daha az boşluk

    -- 📌 Parti İsmi (kompakt pozisyon)
    local title_y = base_y - 22
    draw.SimpleText(party.language["Party Name"] .. ": " .. partyData.name, "comfortaa_big", 
        base_x + (panel_width / 2), title_y, color_text, TEXT_ALIGN_CENTER)

    for index, steamID in ipairs(members) do
        local pos_x = base_x
        local pos_y = base_y + (index - 1) * (panel_height + margin)

        -- 📌 Panel Arka Planı (kompakt boyut)
        draw.RoundedBox(6, pos_x, pos_y, panel_width, panel_height, color_background)
        draw.RoundedBox(6, pos_x + 2, pos_y + 2, panel_width - 4, panel_height - 4, color_panel)

        if IsValid(player.GetBySteamID64(steamID)) then
            local member = player.GetBySteamID64(steamID)
            local health = math.Clamp(100 * (1 / (member:GetMaxHealth() / member:Health())), 0, 100)
            local armor = math.Clamp(member:Armor(), 0, 100)

            -- 📌 Rank bilgilerini al
            local rankColor = Color(255, 255, 255)
            local rankName = ""

            -- Önce doğrudan parti verilerinden al (daha güvenilir)
            local memberPartyID = member:GetParty()
            if memberPartyID and parties[memberPartyID] and parties[memberPartyID].memberRanks then
                local rankID = parties[memberPartyID].memberRanks[member:SteamID64()] or "member"
                if parties[memberPartyID].ranks and parties[memberPartyID].ranks[rankID] then
                    rankColor = parties[memberPartyID].ranks[rankID].color or Color(255, 255, 255)
                    rankName = parties[memberPartyID].ranks[rankID].name or ""
                end
            end

            -- Fallback: Fonksiyonları dene
            if (rankName == "" or rankName == "Üye") and member.GetPartyRankName then
                local funcRankName = member:GetPartyRankName()
                if funcRankName and funcRankName != "" then
                    rankName = funcRankName
                end
            end
            if rankColor.r == 255 and rankColor.g == 255 and rankColor.b == 255 and member.GetPartyRankColor then
                local funcRankColor = member:GetPartyRankColor()
                if funcRankColor then
                    rankColor = funcRankColor
                end
            end

            -- 📌 İsim (kompakt pozisyon)
            local name_x = pos_x + 8
            local name_y = pos_y + 3
            draw.SimpleText(member:Nick(), "comfortaa_medium", name_x, name_y, rankColor, TEXT_ALIGN_LEFT)

            -- 📌 Rank ve Job (tek satırda, kompakt)
            local info_y = pos_y + 18
            local rankText = ""
            local jobText = ""

            if rankName != "" and rankName != "Üye" then
                rankText = "[" .. rankName .. "]"
            end

            if member.getDarkRPVar then
                jobText = member:getDarkRPVar("job") or ""
                if string.len(jobText) > 12 then
                    jobText = string.Left(jobText, 12) .. "..."
                end
            end

            -- Rank ve Job'ı yan yana kompakt göster
            if rankText != "" and jobText != "" then
                draw.SimpleText(rankText .. " - " .. jobText, "comfortaa_small", name_x, info_y, team.GetColor(member:Team()), TEXT_ALIGN_LEFT)
            elseif rankText != "" then
                draw.SimpleText(rankText, "comfortaa_small", name_x, info_y, rankColor, TEXT_ALIGN_LEFT)
            elseif jobText != "" then
                draw.SimpleText(jobText, "comfortaa_small", name_x, info_y, team.GetColor(member:Team()), TEXT_ALIGN_LEFT)
            end

            -- 📌 Kompakt Sağlık & Zırh Çubukları
            local bar_x = pos_x + 8
            local health_bar_y = pos_y + 32
            local bar_width = panel_width - 60  -- Sağ tarafa yer bırak
            local health_bar_width = bar_width * (health / 100)
            local armor_bar_width = bar_width * (armor / 100)

            -- Sağlık çubuğu
            draw.RoundedBox(1, bar_x, health_bar_y, bar_width, 4, Color(40, 40, 40, 200))
            draw.RoundedBox(1, bar_x, health_bar_y, health_bar_width, 4, color_health)

            -- Zırh çubuğu (sağlığın altında)
            if armor > 0 then
                draw.RoundedBox(1, bar_x, health_bar_y + 5, bar_width, 3, Color(40, 40, 40, 200))
                draw.RoundedBox(1, bar_x, health_bar_y + 5, armor_bar_width, 3, color_armor)
            end

            -- 📌 Sağ tarafta durum bilgisi (kompakt)
            local status_x = pos_x + panel_width - 8
            local status_y = pos_y + 30
            local statusText = member:Alive() and (member:Health() .. "/" .. member:Armor()) or "Ölü"
            local statusColor = member:Alive() and color_text or color_dead
            draw.SimpleText(statusText, "comfortaa_small", status_x, status_y, statusColor, TEXT_ALIGN_RIGHT)

            -- 📌 Lider İkonu (sağ üstte kompakt)
            if steamID == partyData.leader or steamID == partyID then
                local leader_icon_x = pos_x + panel_width - 18
                local leader_icon_y = pos_y + 3
                
                surface.SetDrawColor(255, 215, 0, 255)
                surface.SetMaterial(mat_leader)
                surface.DrawTexturedRect(leader_icon_x, leader_icon_y, icon_size, icon_size)

                -- Lider yazısı (daha küçük)
                draw.SimpleText("★", "comfortaa_small", leader_icon_x + icon_size + 2, leader_icon_y, Color(255, 215, 0), TEXT_ALIGN_LEFT)
            end

            -- 📌 Wanted İkonu (lider ikonunun yanında)
            if member:isWanted() then
                local wanted_icon_x = pos_x + panel_width - 35
                local wanted_icon_y = pos_y + 3
                
                surface.SetDrawColor(255, 50, 50, 255)
                surface.SetMaterial(mat_wanted)
                surface.DrawTexturedRect(wanted_icon_x, wanted_icon_y, icon_size, icon_size)
            end
        else
            -- 📌 Offline Durumu (kompakt)
            local offlineName = "Offline Player"
            local rankColor = Color(150, 150, 150)
            local rankName = ""

            if partyData.memberRanks and partyData.memberRanks[steamID] then
                local rankID = partyData.memberRanks[steamID]
                if partyData.ranks and partyData.ranks[rankID] then
                    rankName = partyData.ranks[rankID].name or ""
                    local originalColor = partyData.ranks[rankID].color or Color(255, 255, 255)
                    rankColor = Color(originalColor.r * 0.6, originalColor.g * 0.6, originalColor.b * 0.6)
                end
            end

            local name_x = pos_x + 8
            local name_y = pos_y + 3
            draw.SimpleText(offlineName, "comfortaa_medium", name_x, name_y, rankColor, TEXT_ALIGN_LEFT)

            local info_y = pos_y + 18
            local rankText = ""
            if rankName != "" and rankName != "Üye" then
                rankText = "[" .. rankName .. "] - Offline"
            else
                rankText = "Offline"
            end
            draw.SimpleText(rankText, "comfortaa_small", name_x, info_y, color_dead, TEXT_ALIGN_LEFT)

            -- Offline lider ikonu
            if steamID == partyData.leader or steamID == partyID then
                local leader_icon_x = pos_x + panel_width - 18
                local leader_icon_y = pos_y + 3
                
                surface.SetDrawColor(120, 120, 120, 255)
                surface.SetMaterial(mat_leader)
                surface.DrawTexturedRect(leader_icon_x, leader_icon_y, icon_size, icon_size)
                
                draw.SimpleText("★", "comfortaa_small", leader_icon_x + icon_size + 2, leader_icon_y, Color(120, 120, 120), TEXT_ALIGN_LEFT)
            end
        end
    end
end)





local function PartyOpenCon()
if ( IsValid( Party_Panel ) ) then Party_Panel:Remove() end
	if parties[LocalPlayer():GetParty()] then
		Party_Panel = vgui.Create("DFrame", g_ContextMenu)
		Party_Panel:SetPos(party.hudhorizontalpos, party.hudverticalpos)
		Party_Panel:SetSize( 175, #parties[LocalPlayer():GetParty()].members * 60 + 30)
		Party_Panel:ShowCloseButton(false)
		Party_Panel:SetTitle("")
		--↔↕
		--Party_Paneldp = vgui.Create( "DPanel", g_ContextMenu )
		Party_Panel:SetMouseInputEnabled(true)
		function Party_Panel.Paint()
			if parties[LocalPlayer():GetParty()] then
				draw.RoundedBox(5,0,0, 175, #parties[LocalPlayer():GetParty()].members * 60 + 30, Color( 25, 25, 25, 50 ) ) 
				draw.DrawText("↕", "roboto16", 165,0, Color( 255, 255, 255, 255 ), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER )
				draw.DrawText("↔", "roboto16", 165,0, Color( 255, 255, 255, 255 ), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER )
				local hudx,hudy = Party_Panel:GetPos()
				party.hudhorizontalpos , party.hudverticalpos = math.Clamp(hudx , 0,ScrW() - 175) , math.Clamp(hudy , 0, ScrH() - (#parties[LocalPlayer():GetParty()].members * 60 + 30))
			end
		end
		if parties[LocalPlayer():SteamID64()] then
			for v,k in pairs (parties[LocalPlayer():GetParty()].members) do
				local position = (v * 60 + 2 )
				Party_PanelButton = vgui.Create( "PartyButton", Party_Panel)
				Party_PanelButton:SetText(party.language["Kick"])
				Party_PanelButton:SetPos(97, position)
				Party_PanelButton:SetSize(50, 20)
				function Party_PanelButton:DoClick() -- Defines what should happen when the label is clicked
					self:Remove()
					net.Start( "KickFromParty" )
					net.WriteString(k)
					net.SendToServer()
				end
			end
		end
	end
end
hook.Add( "OnContextMenuOpen", "PartyOpenCon", PartyOpenCon )

local function PartyCloseCon()
	GetConVar( "party.hudhorizontalpos" ):SetInt( party.hudhorizontalpos)
	GetConVar( "party.hudverticalpos" ):SetInt(party.hudverticalpos)
end
hook.Add( "OnContextMenuClose", "PartyCloseCon", PartyCloseCon )



--surface.CreateFont( "party_hud_font1", {
	-- font = "seguisb",
	-- size = 18,
	-- weight = 800,
	-- antialias = true,
	-- underline = false,
	-- italic = false,
	-- strikeout = false,
	-- symbol = false,
	-- rotary = false,
	-- shadow = false,
	-- additive = false,
	-- outline = false,
-- })

-- hook.Add( "HUDPaint", "drawpartyhud", function()
	-- if GetConVar( "party_showhud" ):GetInt() != 1 then
		-- if parties != nil and parties[LocalPlayer():GetParty()] != nil and parties[LocalPlayer():GetParty()].members != nil then
			-- draw.DrawText(party.language["Party Name"] ..": " ..parties[LocalPlayer():GetParty()].name, "roboto16", party.hudhorizontalpos, party.hudverticalpos+10, Color( 255, 255, 255, 255 ), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER )
			-- for v,k in pairs (parties[LocalPlayer():GetParty()].members) do
				-- local position = v * 55 - 30 + v * 5 + party.hudverticalpos
				-- local member = player.GetBySteamID64(k)
				-- draw.RoundedBox(4,party.hudhorizontalpos,position,150,55, party.backgroundcolor ) 
				-- draw.RoundedBox(4,1+ party.hudhorizontalpos,position+1,150-2,55-2,Color(34,34,34,225))
				-- if player.GetBySteamID64(k) !=  false then
				-- local health =  math.Clamp(100*(1/(member:GetMaxHealth() / member:Health())),0,100)
				-- local armor = math.Clamp(  member:Armor(), 0, 100 )
				-- draw.RoundedBoxEx( 2, 4+ party.hudhorizontalpos, position + 45 , 2.13* health/1.5, 5, Color( 192, 57, 43, 255 ), true, true, true, true )
				-- draw.RoundedBoxEx( 2, 4+ party.hudhorizontalpos, position + 48 , 2.13* armor/1.5, 3, Color( 41, 128, 185, 255 ), true, true, true, true )
				-- draw.DrawText(string.Left(member:Nick(), 18), "roboto16", 5+ party.hudhorizontalpos, position+ 5, Color( 255, 255, 255, 255 ), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER )
				-- else 
				-- draw.DrawText(party.language["offline"] , "roboto16", 5+ party.hudhorizontalpos, position+ 5, Color( 255, 255, 255, 255 ), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER )
				-- end
				-- if player.GetBySteamID64(k) !=  false then
					-- if member:Alive() then
					-- draw.DrawText(member:Health().."/"..member:GetMaxHealth(), "roboto16", 5+ party.hudhorizontalpos, position+ 25, Color( 255, 255, 255, 255 ), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER )
					-- else
					-- draw.DrawText( party.language["Dead"] , "roboto16", 5+ party.hudhorizontalpos, position+ 25, Color( 255, 255, 255, 255 ), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER )
					-- end
					-- if (DarkRP) then
						-- if member:getDarkRPVar("job") != nil then
							-- if string.len(member:getDarkRPVar("job")) >= 15 then
								-- draw.DrawText( string.Left(member:getDarkRPVar("job"), 13 ).."..", "roboto16", 145+ party.hudhorizontalpos, position+ 25, team.GetColor( member:Team( ) ), TEXT_ALIGN_RIGHT, TEXT_ALIGN_CENTER )
							-- else
								-- draw.DrawText( string.Left(member:getDarkRPVar("job"), 16 ), "roboto16", 145+ party.hudhorizontalpos, position+ 25, team.GetColor( member:Team( ) ), TEXT_ALIGN_RIGHT, TEXT_ALIGN_CENTER )
							-- end
						-- end
						-- if member:isWanted() then
							-- surface.SetMaterial( Material( wantedicon ) )
							-- surface.SetDrawColor( 255, 255, 255, 255 )
							-- surface.DrawTexturedRect( 155+ party.hudhorizontalpos, position + 34, 16, 16 )
						-- else
							-- surface.SetMaterial( Material( wantedicon ) )
							-- surface.SetDrawColor( 255, 255, 255, party.fadediconsfadeamount )
							-- surface.DrawTexturedRect( 155+ party.hudhorizontalpos, position+ 34, 16, 16 )				
						-- end
					-- end
				-- end
				-- if player.GetBySteamID64(k) != false then
					-- surface.SetMaterial( Material( disconnectedicon ) )
					-- surface.SetDrawColor( 255, 255, 255, party.fadediconsfadeamount )
					-- surface.DrawTexturedRect( 155+ party.hudhorizontalpos, position + 16, 16, 16 )
				-- else
					-- surface.SetMaterial( Material( disconnectedicon ) )
					-- surface.SetDrawColor( 255, 255, 255, 255 )
					-- surface.DrawTexturedRect( 155+ party.hudhorizontalpos, position+ 16, 16, 16 )				
				-- end
				
				-- if k == LocalPlayer():GetParty() then
					-- surface.SetMaterial( Material( leadericon ) )
					-- surface.SetDrawColor( 255, 255, 255, 255 )
					-- surface.DrawTexturedRect( 155+ party.hudhorizontalpos, position + 0, 16, 16 )
				-- else
					-- surface.SetMaterial( Material( leadericon ) )
					-- surface.SetDrawColor( 255, 255, 255, party.fadediconsfadeamount )
					-- surface.DrawTexturedRect( 155+ party.hudhorizontalpos, position+ 0, 16, 16 )				
				-- end	
			-- end
		-- end
		
		-- local tr = LocalPlayer():GetEyeTrace()
		-- local ply = tr.Entity

		-- if ply:IsValid() and ply:IsPlayer() then
			-- if (ply:GetPos():DistToSqr(LocalPlayer():GetPos()) < 45000) and (parties != nil) and (ply:GetParty() != nil) then
				-- local partyhudname = ply:GetPartyName()
				-- draw.SimpleText("Party: " .. partyhudname, "party_hud_font1", ScrW()/2, ScrH() - 129, Color(0,0,0,255), TEXT_ALIGN_CENTER, TEXT_ALIGN_BOTTOM)
				-- draw.SimpleText("Party: " .. partyhudname, "party_hud_font1", ScrW()/2, ScrH() - 130, Color(255,255,255,255), TEXT_ALIGN_CENTER, TEXT_ALIGN_BOTTOM)
			-- end
		-- end
	-- end
-- end)

-- HUD Test Komutları
concommand.Add("test_hud_ranks", function()
	print("=== Parti HUD Rank Test ===")

	local ply = LocalPlayer()
	local partyID = ply:GetParty()

	if not partyID then
		print("❌ Parti bulunamadı! Önce parti oluşturun.")
		return
	end

	print("✅ Parti ID: " .. partyID)
	print("✅ Oyuncu Steam ID: " .. ply:SteamID64())
	print("✅ Lider mi: " .. tostring(ply:SteamID64() == partyID))

	-- Rank fonksiyonlarını test et
	if ply.GetPartyRankColor then
		local rankColor = ply:GetPartyRankColor()
		print("✅ Rank Rengi: " .. rankColor.r .. "," .. rankColor.g .. "," .. rankColor.b)
	else
		print("❌ GetPartyRankColor fonksiyonu bulunamadı!")
	end

	if ply.GetPartyRankName then
		local rankName = ply:GetPartyRankName()
		print("✅ Rank Adı: " .. rankName)
	else
		print("❌ GetPartyRankName fonksiyonu bulunamadı!")
	end

	-- Parti verilerini kontrol et
	if parties[partyID] then
		print("\n--- Parti Verileri ---")
		print("Parti Adı: " .. parties[partyID].name)

		if parties[partyID].memberRanks then
			print("Üye Rütbeleri:")
			for memberID, rankID in pairs(parties[partyID].memberRanks) do
				local member = player.GetBySteamID64(memberID)
				local memberName = member and member:Nick() or "Offline"
				print("  " .. memberName .. " (" .. memberID .. ") -> " .. rankID)
			end
		end

		if parties[partyID].ranks then
			print("Mevcut Rütbeler:")
			for rankID, rankData in pairs(parties[partyID].ranks) do
				local color = rankData.color
				print("  " .. rankID .. ": " .. rankData.name .. " (R:" .. color.r .. " G:" .. color.g .. " B:" .. color.b .. ")")
			end
		end
	else
		print("❌ Parti verileri bulunamadı!")
	end

	print("\n=== Test Tamamlandı ===")
end)

concommand.Add("toggle_party_hud", function()
	local currentValue = GetConVar("party_showhud"):GetInt()
	local newValue = currentValue == 1 and 0 or 1

	GetConVar("party_showhud"):SetInt(newValue)

	if newValue == 0 then
		print("✅ Parti HUD gizlendi")
	else
		print("✅ Parti HUD gösteriliyor")
	end
end)

concommand.Add("hud_info", function()
	print("=== Parti HUD Bilgileri ===")
	print("Panel Genişliği: 240px")
	print("Panel Yüksekliği: 48px")
	print("Pozisyon: Sol üst köşe (20, 80)")
	print("Özellikler:")
	print("  ✅ Rank renkleri")
	print("  ✅ Lider ikonu (LEADER)")
	print("  ✅ Sağlık/Zırh çubukları")
	print("  ✅ Offline oyuncu desteği")
	print("  ✅ Job bilgileri")
	print("========================")
end)

concommand.Add("test_fonts", function()
	print("=== Font Test ===")
	print("comfortaa_big: " .. tostring(surface.GetFont("comfortaa_big") ~= nil))
	print("comfortaa_medium: " .. tostring(surface.GetFont("comfortaa_medium") ~= nil))
	print("comfortaa_small: " .. tostring(surface.GetFont("comfortaa_small") ~= nil))
	print("roboto16: " .. tostring(surface.GetFont("roboto16") ~= nil))
	print("================")
end)

-- HUD Yenileme Sistemi
local lastPartyUpdate = 0
local hudRefreshRate = 5 -- 0.2 saniyede bir kontrol et (daha hızlı)
local lastMemberRanks = {}

-- Parti verilerini kontrol et ve değişiklik varsa HUD'u yenile
local function CheckPartyUpdates()
    local ply = LocalPlayer()
    if not IsValid(ply) then return end

    local partyID = ply:GetParty()
    if not partyID or not parties[partyID] then
        lastMemberRanks = {}
        return
    end

    -- Üye rütbelerini kontrol et
    local currentMemberRanks = {}
    if parties[partyID].memberRanks then
        for memberID, rankID in pairs(parties[partyID].memberRanks) do
            currentMemberRanks[memberID] = rankID
        end
    end

    -- Değişiklik var mı kontrol et
    local hasChanged = false

    -- Yeni üye var mı veya rütbe değişti mi?
    for memberID, rankID in pairs(currentMemberRanks) do
        if lastMemberRanks[memberID] != rankID then
            hasChanged = true
            print("[Party HUD] Rank değişikliği tespit edildi: " .. memberID .. " -> " .. rankID)
            break
        end
    end

    -- Üye çıkarıldı mı?
    for memberID, rankID in pairs(lastMemberRanks) do
        if not currentMemberRanks[memberID] then
            hasChanged = true
            print("[Party HUD] Üye çıkarıldı: " .. memberID)
            break
        end
    end

    if hasChanged then
        lastMemberRanks = table.Copy(currentMemberRanks)
        party.hudNeedsRefresh = true
        print("[Party HUD] HUD yenileniyor...")
    end
end

-- Timer ile sürekli kontrol et
timer.Create("PartyHUDRefresh", hudRefreshRate, 0, CheckPartyUpdates)

-- Network mesajlarını dinle
net.Receive("oneparty", function()
    print("[Party HUD] oneparty mesajı alındı")
    -- Parti verileri güncellendiğinde HUD'u yenile
    party.hudNeedsRefresh = true
    timer.Simple(0.05, function()
        CheckPartyUpdates()
    end)
end)

net.Receive("onepartytoparty", function()
    print("[Party HUD] onepartytoparty mesajı alındı")
    -- Parti üyelerine özel güncellemeler
    party.hudNeedsRefresh = true
    timer.Simple(0.05, function()
        CheckPartyUpdates()
    end)
end)

-- Parti verilerini alan network receiver
net.Receive("party", function()
    print("[Party HUD] party mesajı alındı")
    party.hudNeedsRefresh = true
    timer.Simple(0.05, function()
        CheckPartyUpdates()
    end)
end)

-- Manuel yenileme komutu
concommand.Add("refresh_party_hud", function()
    party.hudNeedsRefresh = true
    lastMemberRanks = {}
    CheckPartyUpdates()
    print("✅ Parti HUD yenilendi!")
end)

-- Debug komutu
concommand.Add("debug_party_ranks", function()
    local ply = LocalPlayer()
    local partyID = ply:GetParty()

    if not partyID or not parties[partyID] then
        print("❌ Parti bulunamadı!")
        return
    end

    print("=== Debug Parti Ranks ===")
    print("Parti ID: " .. partyID)

    if parties[partyID].memberRanks then
        print("Üye Rütbeleri:")
        for memberID, rankID in pairs(parties[partyID].memberRanks) do
            local member = player.GetBySteamID64(memberID)
            local memberName = member and member:Nick() or "Offline"
            print("  " .. memberName .. " (" .. memberID .. ") = " .. rankID)
        end
    else
        print("❌ memberRanks tablosu bulunamadı!")
    end

    if parties[partyID].ranks then
        print("Mevcut Rütbeler:")
        for rankID, rankData in pairs(parties[partyID].ranks) do
            print("  " .. rankID .. " = " .. rankData.name)
        end
    else
        print("❌ ranks tablosu bulunamadı!")
    end

    print("========================")
end)