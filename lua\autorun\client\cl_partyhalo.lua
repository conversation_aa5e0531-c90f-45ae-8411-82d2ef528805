hook.Add("InitPostEntity", "partyholo", function()
    local ply = LocalPlayer()
    local renk = Color(0,255,0,255)
    local halod = {}
    timer.Create("partyholotable", 3, 0, function()
        halod = {}
    if party.halos and GetConVar( "party_lowend" ):GetInt() == 1 then 
        local getparty = ply:GetParty()
        if parties != nil and parties[getparty] != nil and parties[getparty].members != nil then
            for v,k in pairs(parties[getparty].members) do
                local member = player.GetBySteamID64(k)
                if member !=  false and member != ply and member:Alive() then
                    table.insert(halod, member)
                end
            end
        end
        renk = Color(GetConVar( "color_phalo_r" ):GetInt(),GetConVar( "color_phalo_g" ):GetInt(),GetConVar( "color_phalo_b" ):GetInt(),GetConVar( "color_phalo_a" ):GetInt())
    end
    end)
    
    hook.Add( "PreDrawHalos", "drawpartyhalo", function() 
    
            halo.Add(halod, renk, 2, 2, 4)
   
    end)
    

    /*
    hook.Add( "PostDrawOpaqueRenderables", "paintspritesparty", function()
        if party.halos and GetConVar( "party_lowend" ):GetInt() == 1 then
            if parties != nil and parties[ply:GetParty()] != nil and parties[ply:GetParty()].members != nil then
                for v,k in pairs (parties[ply:GetParty()].members) do
                    local member = player.GetBySteamID64(k)
                    if member !=  false and member != LocalPlayer() and member:Alive() then
                        local partymemberpos = member:GetPos()
                        cam.Start3D2D(partymemberpos + Vector(0,0,1), Angle( 0, 0, 0 ), 1) 
                            surface.DrawCircle( 0, 0, 20 ,Color(GetConVar( "color_phalo_r" ):GetInt(),GetConVar( "color_phalo_g" ):GetInt(),GetConVar( "color_phalo_b" ):GetInt(),GetConVar( "color_phalo_a" ):GetInt()) )
                        cam.End3D2D()
                    end
                end
            end
        end
    end )
    */
end)

net.Receive("mitajan_hud", function()
    local bool = net.ReadBool()

    if bool == true then 
        hook.Add( "PostDrawOpaqueRenderables", "mithud_sprites", function()
        for i, v in player.Iterator() do
            local member = v
            if member != LocalPlayer() and member:Alive() and member:GetNWBool("mitekip", false) == true then
                local partymemberpos = member:GetPos()
                cam.Start3D2D(partymemberpos + Vector(0,0,1), Angle( 0, 0, 0 ), 1) 
                    surface.DrawCircle( 0, 0, 20 ,Color(0,77,230,155) )
                cam.End3D2D()
            end
        end
        end )
    end 

    if bool == false then 
        hook.Remove( "PostDrawOpaqueRenderables", "mithud_sprites")
    end 
end)