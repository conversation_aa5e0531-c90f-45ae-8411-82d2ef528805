-- Parti Rank Sistemi Test Dosyası
-- Bu dosyayı sunucuda çalıştırarak rank sistemini test edebilirsiniz

if SERVER then
    -- Test fonksiyonları
    concommand.Add("test_party_ranks", function(ply, cmd, args)
        if not ply:IsAdmin() then return end
        
        print("=== Parti Rank Sistemi Test Başlıyor ===")
        
        -- Test 1: Parti oluşturma
        ply:Startparty("Test Partisi")
        print("✓ Parti oluşturuldu: " .. ply:GetPartyName())
        
        -- Test 2: Default rankları kontrol et
        local partyID = ply:GetParty()
        if parties[partyID] and parties[partyID].ranks then
            print("✓ Default ranklar yüklendi:")
            for rankID, rankData in pairs(parties[partyID].ranks) do
                print("  - " .. rankID .. ": " .. rankData.name)
            end
        end
        
        -- Test 3: Oyuncunun rütbesini kontrol et
        local playerRank = ply:GetPartyRank()
        print("✓ Oyuncu rütbesi: " .. playerRank)
        
        -- Test 4: İzin kontrolü
        local hasKickPerm = ply:HasPartyPermission("kick")
        print("✓ Kick izni: " .. tostring(hasKickPerm))
        
        print("=== Test Tamamlandı ===")
    end)
    
    concommand.Add("test_create_rank", function(ply, cmd, args)
        if not ply:IsAdmin() then return end
        
        local rankID = args[1] or "test_rank"
        local rankName = args[2] or "Test Rütbesi"
        
        ply:CreatePartyRank(rankID, rankName, Color(255, 100, 100), {
            kick = true,
            invite = false,
            promote = false,
            demote = false,
            createRank = false,
            transferLeadership = false,
            disbandParty = false
        })
        
        print("Test rütbesi oluşturuldu: " .. rankName)
    end)
    
    concommand.Add("test_promote_player", function(ply, cmd, args)
        if not ply:IsAdmin() then return end
        
        local targetName = args[1]
        local rankID = args[2] or "test_rank"
        
        if not targetName then
            print("Kullanım: test_promote_player <oyuncu_adı> [rank_id]")
            return
        end
        
        local targetPlayer = nil
        for _, p in pairs(player.GetAll()) do
            if string.lower(p:Nick()) == string.lower(targetName) then
                targetPlayer = p
                break
            end
        end
        
        if not targetPlayer then
            print("Oyuncu bulunamadı: " .. targetName)
            return
        end
        
        ply:PromotePlayer(targetPlayer:SteamID64(), rankID)
        print("Oyuncu terfi ettirildi: " .. targetPlayer:Nick() .. " -> " .. rankID)
    end)
    
    concommand.Add("test_transfer_leadership", function(ply, cmd, args)
        if not ply:IsAdmin() then return end
        
        local targetName = args[1]
        
        if not targetName then
            print("Kullanım: test_transfer_leadership <oyuncu_adı>")
            return
        end
        
        local targetPlayer = nil
        for _, p in pairs(player.GetAll()) do
            if string.lower(p:Nick()) == string.lower(targetName) then
                targetPlayer = p
                break
            end
        end
        
        if not targetPlayer then
            print("Oyuncu bulunamadı: " .. targetName)
            return
        end
        
        ply:TransferLeadership(targetPlayer:SteamID64())
        print("Liderlik devredildi: " .. targetPlayer:Nick())
    end)
    
    -- Debug fonksiyonu
    concommand.Add("debug_party_data", function(ply, cmd, args)
        if not ply:IsAdmin() then return end
        
        local partyID = ply:GetParty()
        if not partyID or not parties[partyID] then
            print("Oyuncu partide değil!")
            return
        end
        
        print("=== Parti Verileri ===")
        print("Parti ID: " .. partyID)
        print("Parti Adı: " .. parties[partyID].name)
        
        print("\nÜyeler:")
        for _, memberID in pairs(parties[partyID].members) do
            local member = player.GetBySteamID64(memberID)
            local memberName = member and member:Nick() or "Offline"
            local rankID = parties[partyID].memberRanks[memberID] or "member"
            local rankName = parties[partyID].ranks[rankID] and parties[partyID].ranks[rankID].name or "Bilinmeyen"
            print("  - " .. memberName .. " (" .. memberID .. ") - " .. rankName .. " (" .. rankID .. ")")
        end
        
        print("\nRütbeler:")
        for rankID, rankData in pairs(parties[partyID].ranks) do
            print("  - " .. rankID .. ": " .. rankData.name)
            print("    Renk: " .. rankData.color.r .. "," .. rankData.color.g .. "," .. rankData.color.b)
            print("    İzinler:")
            for perm, value in pairs(rankData.permissions) do
                print("      " .. perm .. ": " .. tostring(value))
            end
        end
    end)
    
    print("Parti Rank Sistemi test komutları yüklendi:")
    print("  - test_party_ranks: Genel test")
    print("  - test_create_rank [rank_id] [rank_name]: Rütbe oluştur")
    print("  - test_promote_player <oyuncu_adı> [rank_id]: Oyuncu terfi ettir")
    print("  - test_transfer_leadership <oyuncu_adı>: Liderlik devret")
    print("  - debug_party_data: Parti verilerini göster")
end
