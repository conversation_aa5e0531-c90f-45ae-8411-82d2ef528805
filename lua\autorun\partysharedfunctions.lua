local meta = FindMetaTable("Player")

function meta:GetParty()-- Returns the party the player is currently in
local party								
	for v, k in pairs(parties) do
		if table.HasValue(parties[v].members, self:SteamID64()) then
			party = v	
		end
	end
	return party
end

function meta:GetPartyName()							-- Returns the party name
	if self:GetParty() != nil then
		return parties[self:GetParty()].name
	end
end

if CLIENT then
	 concommand.Add( "PrintParty", function(ply,_e,args)
		if ply:GetParty() then
			PrintTable(parties[ply:GetParty()])
		end
	 end )
end


-- hook.Add("CanJoinParty" , "ForceSameTeam" , function (ply, partyid)
	-- if ply:Team() != player.GetBySteamID64(partyid):Team() then
		-- ply:Notify("That's a different team's party!")
		-- return false
	-- end
	-- return true
-- end)

-- Oyuncunun parti içindeki rütbesini alma
function meta:GetPartyRank()
    if not self:GetParty() then return nil end
    
    local partyID = self:GetParty()
    if not parties[partyID] or not parties[partyID].memberRanks then return nil end
    
    return parties[partyID].memberRanks[self:SteamID64()] or "member"
end

-- Oyuncunun parti içindeki rütbe rengini alma
function meta:GetPartyRankColor()
    if not self:GetParty() then return Color(255, 255, 255) end
    
    local partyID = self:GetParty()
    local rankID = self:GetPartyRank()
    
    if not parties[partyID] or not parties[partyID].ranks or not parties[partyID].ranks[rankID] then
        return Color(255, 255, 255)
    end
    
    return parties[partyID].ranks[rankID].color or Color(255, 255, 255)
end

-- Oyuncunun parti içindeki rütbe adını alma
function meta:GetPartyRankName()
    if not self:GetParty() then return "" end
    
    local partyID = self:GetParty()
    local rankID = self:GetPartyRank()
    
    if not parties[partyID] or not parties[partyID].ranks or not parties[partyID].ranks[rankID] then
        return ""
    end
    
    return parties[partyID].ranks[rankID].name or ""
end

-- Oyuncunun belirli bir izne sahip olup olmadığını kontrol etme
function meta:HasPartyPermission(permissionName)
    if not self:GetParty() then return false end
    
    -- Parti lideri her zaman tüm izinlere sahiptir
    if self:SteamID64() == self:GetParty() then return true end
    
    local partyID = self:GetParty()
    local rankID = self:GetPartyRank()
    
    if not parties[partyID] or not parties[partyID].ranks or not parties[partyID].ranks[rankID] then
        return false
    end
    
    return parties[partyID].ranks[rankID].permissions[permissionName] or false
end

